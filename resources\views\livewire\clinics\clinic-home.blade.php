<div class="contents">
    <!-- Main Content Wrapper -->
    <main class="main-content w-full pb-8">
        <div class="flex items-center space-x-4 py-5 lg:py-6 px-[var(--margin-x)]">
            <h2 class="text-xl font-medium text-slate-800 dark:text-navy-50 lg:text-2xl">
                <a class="text-primary transition-colors hover:text-primary-focus dark:text-accent-light dark:hover:text-accent"
                    href="javascript:history.back()">{{ $header['header'] }}</a>
            </h2>
            <div class="hidden h-full py-1 sm:flex">
                <div class="h-full w-px bg-slate-300 dark:bg-navy-600"></div>
            </div>
            <ul class="hidden flex-wrap items-center space-x-2 sm:flex">
                <li>{{ $header['subHeader'] }}</li>
            </ul>
        </div>
        <div
            class="mt-4 grid grid-cols-12 gap-4 px-[var(--margin-x)] transition-all duration-[.25s] sm:mt-5 sm:gap-5 lg:mt-6 lg:gap-6">
            <div class="card col-span-12 lg:col-span-8 xl:col-span-9">
                <div class="mt-3 flex items-center justify-between px-4 sm:px-5">
                    <h2 class="text-base font-medium tracking-wide text-slate-700 line-clamp-1 dark:text-navy-100">
                        Clinic Overview
                    </h2>
                    <div class="flex">
                        <div class="flex items-center" x-data="{ isInputActive: false }">
                            <label class="block">
                                <input x-effect="isInputActive === true && $nextTick(() => { $el.focus()});"
                                    :class="isInputActive ? 'w-32 lg:w-48' : 'w-0'"
                                    class="form-input bg-transparent px-1 text-right transition-all duration-100 placeholder:text-slate-500 dark:placeholder:text-navy-200"
                                    placeholder="Search here..." type="text" />
                            </label>
                            <button @click="isInputActive = !isInputActive"
                                class="btn size-8 rounded-full p-0 hover:bg-slate-300/20 focus:bg-slate-300/20 active:bg-slate-300/25 dark:hover:bg-navy-300/20 dark:focus:bg-navy-300/20 dark:active:bg-navy-300/25">
                                <svg xmlns="http://www.w3.org/2000/svg" class="size-4.5" fill="none"
                                    viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5"
                                        d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                                </svg>
                            </button>
                        </div>
                        <div x-data="usePopper({ placement: 'bottom-end', offset: 4 })" @click.outside="if(isShowPopper) isShowPopper = false"
                            class="inline-flex">
                            <button x-ref="popperRef" @click="isShowPopper = !isShowPopper"
                                class="btn -mr-1.5 size-8 rounded-full p-0 hover:bg-slate-300/20 focus:bg-slate-300/20 active:bg-slate-300/25 dark:hover:bg-navy-300/20 dark:focus:bg-navy-300/20 dark:active:bg-navy-300/25">
                                <svg xmlns="http://www.w3.org/2000/svg" class="size-4.5" fill="none"
                                    viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                        d="M12 5v.01M12 12v.01M12 19v.01M12 6a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2z" />
                                </svg>
                            </button>
                            <div x-ref="popperRoot" class="popper-root" :class="isShowPopper && 'show'">
                                <div
                                    class="popper-box rounded-md border border-slate-150 bg-white py-1.5 font-inter dark:border-navy-500 dark:bg-navy-700">
                                    <ul>
                                        <li>
                                            <a href="#"
                                                class="flex h-8 items-center px-3 pr-8 font-medium tracking-wide outline-hidden transition-all hover:bg-slate-100 hover:text-slate-800 focus:bg-slate-100 focus:text-slate-800 dark:hover:bg-navy-600 dark:hover:text-navy-100 dark:focus:bg-navy-600 dark:focus:text-navy-100">Action</a>
                                        </li>
                                        <li>
                                            <a href="#"
                                                class="flex h-8 items-center px-3 pr-8 font-medium tracking-wide outline-hidden transition-all hover:bg-slate-100 hover:text-slate-800 focus:bg-slate-100 focus:text-slate-800 dark:hover:bg-navy-600 dark:hover:text-navy-100 dark:focus:bg-navy-600 dark:focus:text-navy-100">Another
                                                Action</a>
                                        </li>
                                        <li>
                                            <a href="#"
                                                class="flex h-8 items-center px-3 pr-8 font-medium tracking-wide outline-hidden transition-all hover:bg-slate-100 hover:text-slate-800 focus:bg-slate-100 focus:text-slate-800 dark:hover:bg-navy-600 dark:hover:text-navy-100 dark:focus:bg-navy-600 dark:focus:text-navy-100">Something
                                                else</a>
                                        </li>
                                    </ul>
                                    <div class="my-1 h-px bg-slate-150 dark:bg-navy-500"></div>
                                    <ul>
                                        <li>
                                            <a href="#"
                                                class="flex h-8 items-center px-3 pr-8 font-medium tracking-wide outline-hidden transition-all hover:bg-slate-100 hover:text-slate-800 focus:bg-slate-100 focus:text-slate-800 dark:hover:bg-navy-600 dark:hover:text-navy-100 dark:focus:bg-navy-600 dark:focus:text-navy-100">Separated
                                                Link</a>
                                        </li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="mt-5 grid grid-cols-1 gap-4 px-4 sm:grid-cols-3 sm:px-5">
                    <div
                        class="relative flex flex-col overflow-hidden rounded-lg bg-linear-to-br from-info to-info-focus p-3.5">
                        <p class="text-xs uppercase text-sky-100">Doctors</p>
                        <div class="flex items-end justify-between space-x-2">
                            <p class="mt-4 text-2xl font-medium text-white">{{ $clinic->doctors->count() }}</p>
                            <a href="#"
                                class="border-b border-dotted border-current pb-0.5 text-xs font-medium text-sky-100 outline-hidden transition-colors duration-300 line-clamp-1 hover:text-white focus:text-white">
                                show all
                            </a>
                        </div>
                        <div class="mask is-reuleaux-triangle absolute top-0 right-0 -m-3 size-16 bg-white/20"></div>
                    </div>
                    <div
                        class="relative flex flex-col overflow-hidden rounded-lg bg-linear-to-br from-amber-400 to-orange-600 p-3.5">
                        <p class="text-xs uppercase text-amber-50">Services</p>
                        <div class="flex items-end justify-between space-x-2">
                            <p class="mt-4 text-2xl font-medium text-white">
                                {{ $clinic->services->count() }}</p>
                            <a href="#"
                                class="border-b border-dotted border-current pb-0.5 text-xs font-medium text-amber-50 outline-hidden transition-colors duration-300 line-clamp-1 hover:text-white focus:text-white">
                                show all
                            </a>
                        </div>
                        <div class="mask is-diamond absolute top-0 right-0 -m-3 size-16 bg-white/20"></div>
                    </div>
                    <div
                        class="relative flex flex-col overflow-hidden rounded-lg bg-linear-to-br from-pink-500 to-rose-500 p-3.5">
                        <p class="text-xs uppercase text-pink-100">Offers</p>
                        <div class="flex items-end justify-between space-x-2">
                            <p class="mt-4 text-2xl font-medium text-white">
                                {{ $clinic->offers->count() }}</p>
                            <a href="#"
                                class="border-b border-dotted border-current pb-0.5 text-xs font-medium text-pink-100 outline-hidden transition-colors duration-300 line-clamp-1 hover:text-white focus:text-white">
                                show all
                            </a>
                        </div>
                        <div class="mask is-hexagon-2 absolute top-0 right-0 -m-3 size-16 bg-white/20"></div>
                    </div>
                </div>

                @if ($appointments?->count() > 0)
                    <div class="scrollbar-sm mt-5 min-w-full overflow-x-auto">
                        <table class="is-hoverable w-full text-left">
                            <thead class="rounded-none">
                                <tr>
                                    <th
                                        class="whitespace-nowrap bg-slate-200 px-4 py-3 font-semibold uppercase text-slate-800 dark:bg-navy-800 dark:text-navy-100 lg:px-5">
                                        NAME
                                    </th>
                                    <th
                                        class="whitespace-nowrap bg-slate-200 px-4 py-3 font-semibold uppercase text-slate-800 dark:bg-navy-800 dark:text-navy-100 lg:px-5">
                                        DOCTOR
                                    </th>
                                    <th
                                        class="whitespace-nowrap bg-slate-200 px-4 py-3 font-semibold uppercase text-slate-800 dark:bg-navy-800 dark:text-navy-100 lg:px-5">
                                        DATE
                                    </th>
                                    <th
                                        class="whitespace-nowrap bg-slate-200 px-4 py-3 font-semibold uppercase text-slate-800 dark:bg-navy-800 dark:text-navy-100 lg:px-5">
                                        SHIFT
                                    </th>
                                    <th
                                        class="whitespace-nowrap bg-slate-200 px-4 py-3 font-semibold uppercase text-slate-800 dark:bg-navy-800 dark:text-navy-100 lg:px-5">
                                        STATUS
                                    </th>
                                    <th
                                        class="whitespace-nowrap bg-slate-200 font-semibold uppercase text-slate-800 dark:bg-navy-800 dark:text-navy-100 lg:px-5 w-16">
                                        ATTENDED
                                    </th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach ($appointments as $appointment)
                                    <tr class="border-y border-transparent border-b-slate-200 dark:border-b-navy-500">
                                        <td class="whitespace-nowrap px-4 py-3 sm:px-5">
                                            <div class="flex items-center space-x-4">
                                                <div>
                                                    <p class="font-medium text-slate-600 dark:text-navy-100">
                                                        {{ $appointment->customer->name }}
                                                    </p>
                                                    <p class="mt-1 text-xs text-slate-400 dark:text-navy-300">
                                                        {{ $appointment->customer->phone_number }}
                                                    </p>
                                                </div>
                                            </div>
                                        </td>
                                        <td class="whitespace-nowrap px-4 py-3 sm:px-5">
                                            {{ $appointment->doctor->name }}</td>
                                        <td class="whitespace-nowrap px-4 py-3 sm:px-5">
                                            <i class="fa-solid fa-calendar"></i>
                                            &hairsp;
                                            <span>{{ $appointment->date }}</span>
                                        </td>
                                        <td class="whitespace-nowrap px-4 py-3 sm:px-5">
                                            <i class="fa-solid fa-clock"></i>
                                            &hairsp;
                                            <span>{{ $appointment->shift }}</span>
                                        </td>
                                        <td class="whitespace-nowrap px-4 py-3 sm:px-5">
                                            <div
                                                class="badge rounded-full border {{ $appointment->status === 'confirmed' ? 'border-success text-success' : ($appointment->status === 'pending' ? 'border-warning text-warning' : 'border-error text-error') }}">
                                                {{ $appointment->status }}
                                            </div>
                                        </td>
                                        <td class="whitespace-nowrap px-4 py-3 sm:px-5 text-center w-16">
                                            <div
                                                class="badge size-9 rounded-full bg-info/10 p-0 font-medium text-white hover:bg-info/20 focus:bg-info/20 active:bg-info/25">
                                                <i
                                                    class="fa-solid {{ $appointment->attended ? 'fa-check' : 'fa-times' }}"></i>
                                            </div>
                                        </td>
                                    </tr>
                                @endforeach
                            </tbody>
                        </table>
                    </div>

                    {{ $appointments->links('components.pagination1') }}
                @else
                    <div class="flex flex-col items-center justify-center py-8 px-4">
                        <div class="text-slate-400 dark:text-navy-300 mb-2">
                            <svg xmlns="http://www.w3.org/2000/svg" class="size-12" fill="none"
                                viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5" d="M8 10h.01M12 10h.01M16 10h.01M9 16H5a2 2 0 01-2-2V6a2 2 0 012-2h14a2 2 0 012 2v8a2 2 0 01-2 2
                                    h-4m-4 4h.01M12 14h.01M16 14h.01" />
                            </svg>
                        </div>
                        <p class="text-sm">No Appointments Yet</p>
                    </div>
                @endif
            </div>
            <div
                class="col-span-12 grid grid-cols-1 gap-4 sm:grid-cols-2 sm:gap-5 lg:col-span-4 lg:grid-cols-1 lg:gap-6 xl:col-span-3">
                <div class="card pb-5">
                    <div class="my-3 flex h-8 items-center justify-between px-4 sm:px-5">
                        <h2 class="font-medium tracking-wide text-slate-700 line-clamp-1 dark:text-navy-100">
                            Appointments
                        </h2>

                        <div x-data="usePopper({ placement: 'bottom-end', offset: 4 })" @click.outside="if(isShowPopper) isShowPopper = false"
                            class="inline-flex">
                            <button x-ref="popperRef" @click="isShowPopper = !isShowPopper"
                                class="btn -mr-1.5 size-8 rounded-full p-0 hover:bg-slate-300/20 focus:bg-slate-300/20 active:bg-slate-300/25 dark:hover:bg-navy-300/20 dark:focus:bg-navy-300/20 dark:active:bg-navy-300/25">
                                <svg xmlns="http://www.w3.org/2000/svg" class="size-5" fill="none"
                                    viewBox="0 0 24 24" stroke="currentColor" stroke-width="2">
                                    <path stroke-linecap="round" stroke-linejoin="round"
                                        d="M5 12h.01M12 12h.01M19 12h.01M6 12a1 1 0 11-2 0 1 1 0 012 0zm7 0a1 1 0 11-2 0 1 1 0 012 0zm7 0a1 1 0 11-2 0 1 1 0 012 0z" />
                                </svg>
                            </button>

                            <div x-ref="popperRoot" class="popper-root" :class="isShowPopper && 'show'">
                                <div
                                    class="popper-box rounded-md border border-slate-150 bg-white py-1.5 font-inter dark:border-navy-500 dark:bg-navy-700">
                                    <ul>
                                        <li>
                                            <a href="#"
                                                class="flex h-8 items-center px-3 pr-8 font-medium tracking-wide outline-hidden transition-all hover:bg-slate-100 hover:text-slate-800 focus:bg-slate-100 focus:text-slate-800 dark:hover:bg-navy-600 dark:hover:text-navy-100 dark:focus:bg-navy-600 dark:focus:text-navy-100">All</a>
                                        </li>
                                        <li>
                                            <a href="#"
                                                class="flex h-8 items-center px-3 pr-8 font-medium tracking-wide outline-hidden transition-all hover:bg-slate-100 hover:text-slate-800 focus:bg-slate-100 focus:text-slate-800 dark:hover:bg-navy-600 dark:hover:text-navy-100 dark:focus:bg-navy-600 dark:focus:text-navy-100">
                                                Today</a>
                                        </li>
                                        <li>
                                            <a href="#"
                                                class="flex h-8 items-center px-3 pr-8 font-medium tracking-wide outline-hidden transition-all hover:bg-slate-100 hover:text-slate-800 focus:bg-slate-100 focus:text-slate-800 dark:hover:bg-navy-600 dark:hover:text-navy-100 dark:focus:bg-navy-600 dark:focus:text-navy-100">
                                                Yesterday</a>
                                        </li>
                                    </ul>
                                    <div class="my-1 h-px bg-slate-150 dark:bg-navy-500"></div>
                                    <ul>
                                        <li>
                                            <a href="#"
                                                class="flex h-8 items-center px-3 pr-8 font-medium tracking-wide outline-hidden transition-all hover:bg-slate-100 hover:text-slate-800 focus:bg-slate-100 focus:text-slate-800 dark:hover:bg-navy-600 dark:hover:text-navy-100 dark:focus:bg-navy-600 dark:focus:text-navy-100">
                                                show All</a>
                                        </li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div>
                        <div x-init="$nextTick(() => {
                            $el._x_chart = new ApexCharts($el, appointmentAnalytics(
                                {{ $appointments?->where('status', 'confirmed')->count() ?? 0 }},
                                {{ $appointments?->where('status', 'cancelled')->count() ?? 0 }},
                                {{ $appointments?->where('status', 'pending')->count() ?? 0 }}
                            ));
                            $el._x_chart.render()
                        });"></div>
                    </div>
                    <div class="flex justify-center space-x-2">
                        <div class="badge space-x-2.5 text-success dark:text-success-light">
                            <div class="size-2 rounded-full bg-current"></div>
                            <span class="text-xs">Confirmed</span>
                        </div>
                        <div class="badge space-x-2.5 text-error dark:text-error-light">
                            <div class="size-2 rounded-full bg-current"></div>
                            <span class="text-xs">Cancelled</span>
                        </div>
                        <div class="badge space-x-2.5 text-warning dark:text-warning-light">
                            <div class="size-2 rounded-full bg-current"></div>
                            <span class="text-xs">Pending</span>
                        </div>
                    </div>
                </div>
                <div class="card">
                    <div class="grow p-4 sm:p-5">
                        <div class="flex items-center pt-3">
                            <h3 class="text-lg font-medium text-slate-700 dark:text-navy-100">
                                {{ $clinic->name }}
                            </h3>
                            <div
                                class="badge space-x-2.5 my-2.5 {{ $clinic->is_open ? 'text-success' : 'text-error' }}">
                                <div class="size-2 rounded-full bg-current"></div>
                                <span>{{ $clinic->is_open ? 'Open' : 'Closed' }}</span>
                            </div>
                        </div>

                        <div class="my-4 h-px w-full bg-slate-200 dark:bg-navy-500"></div>
                        <div class="grow space-y-4">
                            <div class="flex items-center space-x-2">
                                <div
                                    class="flex size-7 items-center rounded-lg bg-primary/10 p-2 text-primary dark:bg-accent-light/10 dark:text-accent-light">
                                    <i class="fa fa-phone text-xs"></i>
                                </div>
                                <p>{{ $clinic->phone_number }}</p>
                            </div>
                            <div class="flex items-center space-x-2">
                                <div
                                    class="flex size-7 items-center rounded-lg bg-primary/10 p-2 text-primary dark:bg-accent-light/10 dark:text-accent-light">
                                    <i class="fa fa-map-marker text-xs"></i>
                                </div>
                                <p>{{ $clinic->floor_number }} ({{ $clinic->room_number }})</p>
                            </div>
                            <div class="flex items-center space-x-2">
                                <div
                                    class="flex size-7 items-center rounded-lg bg-primary/10 p-2 text-primary dark:bg-accent-light/10 dark:text-accent-light">
                                    <i class="fa fa-clock text-xs"></i>
                                </div>
                                <p>{{ $clinic->open_time }} - {{ $clinic->close_time }}
                                </p>
                            </div>

                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="mt-4 pl-[var(--margin-x)] transition-all duration-[.25s] sm:mt-5 lg:mt-6">
            <div class="rounded-l-lg bg-slate-150 pt-4 pb-1 dark:bg-navy-800">
                <h2
                    class="px-4 text-base font-medium tracking-wide text-slate-700 line-clamp-1 dark:text-navy-100 sm:px-5 lg:text-lg">
                    Services
                </h2>
                <div class="scrollbar-sm mt-4 flex space-x-4 overflow-x-auto px-4 pb-4 sm:px-5">
                    @foreach ($clinic->services as $service)
                        <div class="flex w-72 shrink-0 flex-col">
                            <img class="h-48 w-full rounded-2xl object-cover object-center"
                                src="{{ $doctor->profile_image ?? asset('images/800x600.png') }}" alt="image" />

                            <div class="card mx-2 -mt-8 grow rounded-2xl p-3.5">
                                <div class="mt-2">
                                    <a href="#"
                                        class="text-sm-plus font-medium text-slate-700 hover:text-primary focus:text-primary dark:text-navy-100 dark:hover:text-accent-light dark:focus:text-accent-light">
                                        {{ $service->name }}
                                    </a>
                                    <p class="text-xs text-slate-400 dark:text-navy-300">
                                        {{ $service->description }}
                                    </p>

                                </div>
                                <p class="mt-2">
                                    <span class="text-base font-medium text-slate-700 dark:text-navy-100">
                                        {{ $service->price }}
                                    </span>
                                </p>
                            </div>
                        </div>
                    @endforeach
                </div>
            </div>
        </div>
    </main>

    <script>
        function appointmentAnalytics(confirmed, cancelled, pending) {
            return {
                colors: ["#4ade80", "#f43f5e", "#ffb822"],
                series: [confirmed, cancelled, pending],
                chart: {
                    height: 250,
                    type: "radialBar",
                },
                plotOptions: {
                    radialBar: {
                        hollow: {
                            margin: 10,
                            size: "35%",
                        },
                        track: {
                            margin: 10,
                        },
                        dataLabels: {
                            name: {
                                fontSize: "22px",
                            },
                            value: {
                                fontSize: "16px",
                            },
                            total: {
                                show: true,
                                label: "Total",
                                formatter: function(w) {
                                    return w.config.series.reduce((s, v) => s + v);
                                },
                            },
                        },
                    },
                },
                grid: {
                    padding: {
                        top: -20,
                        bottom: -20,
                        right: 0,
                        left: 0,
                    },
                },
                stroke: {
                    lineCap: "round",
                },
                labels: ["Confirmed", "Cancelled", "Pending"],
            };
        }
    </script>
</div>
