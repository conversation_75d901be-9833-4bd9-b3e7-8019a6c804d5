 <div class="sidebar-panel">
     <div class="flex h-full grow flex-col bg-white pl-[var(--main-sidebar-width)] dark:bg-navy-750">
         <!-- Sidebar Panel Header -->
         <div class="flex h-18 w-full items-center justify-between pl-4 pr-1">
             <div class="flex items-center">
                 <div class="avatar mr-3 hidden size-9 lg:flex">
                     <div class="is-initial rounded-full bg-info/10 text-info">
                         <svg class="size-5" viewBox="0 0 24 24" fill="none" stroke="currentColor"
                             xmlns="http://www.w3.org/2000/svg">
                             <path d="M12.5293 18L20.9999 8.40002" stroke-width="2" stroke-linecap="round"
                                 stroke-linejoin="round" />
                             <path d="M3 13.2L7.23529 18L17.8235 6" stroke-width="2" stroke-linecap="round"
                                 stroke-linejoin="round" />
                         </svg>
                     </div>
                 </div>
                 <p class="text-lg font-medium tracking-wider text-slate-800 dark:text-navy-100">
                     Todo
                 </p>
             </div>
             <button @click="$store.global.isSidebarExpanded = false"
                 class="btn size-7 rounded-full p-0 text-primary hover:bg-slate-300/20 focus:bg-slate-300/20 active:bg-slate-300/25 dark:text-accent-light/80 dark:hover:bg-navy-300/20 dark:focus:bg-navy-300/20 dark:active:bg-navy-300/25 xl:hidden">
                 <svg xmlns="http://www.w3.org/2000/svg" class="size-6" fill="none" viewBox="0 0 24 24"
                     stroke="currentColor">
                     <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7" />
                 </svg>
             </button>
         </div>

         <!-- Sidebar Panel Body -->
         <div class="flex h-[calc(100%-4.5rem)] grow flex-col">
             <div class="is-scrollbar-hidden grow overflow-y-auto">
                 <div class="mt-2 px-4">
                     <button
                         class="btn w-full space-x-2 rounded-full border border-slate-200 py-2 font-medium text-slate-800 hover:bg-slate-150 focus:bg-slate-150 active:bg-slate-150/80 dark:border-navy-500 dark:text-navy-50 dark:hover:bg-navy-500 dark:focus:bg-navy-500 dark:active:bg-navy-500/90">
                         <svg xmlns="http://www.w3.org/2000/svg" class="size-4" fill="none" viewBox="0 0 24 24"
                             stroke="currentColor" stroke-width="1.5">
                             <path stroke-linecap="round" stroke-linejoin="round" d="M12 4v16m8-8H4" />
                         </svg>
                         <span> New Task </span>
                     </button>
                 </div>
                 <ul class="mt-5 space-y-1.5 px-2 font-inter text-xs-plus font-medium">
                     <li>
                         <a class="group flex space-x-2 rounded-lg bg-primary/10 p-2 tracking-wide text-primary outline-hidden transition-all dark:bg-accent-light/10 dark:text-accent-light"
                             href="#">
                             <svg xmlns="http://www.w3.org/2000/svg" class="size-4.5" fill="none" viewBox="0 0 24 24"
                                 stroke="currentColor" stroke-width="1.5">
                                 <path stroke-linecap="round" stroke-linejoin="round"
                                     d="M12 3v1m0 16v1m9-9h-1M4 12H3m15.364 6.364l-.707-.707M6.343 6.343l-.707-.707m12.728 0l-.707.707M6.343 17.657l-.707.707M16 12a4 4 0 11-8 0 4 4 0 018 0z" />
                             </svg>
                             <span>My Day</span>
                         </a>
                     </li>
                     <li>
                         <a class="group flex space-x-2 rounded-lg p-2 tracking-wide text-slate-800 outline-hidden transition-all hover:bg-slate-100 focus:bg-slate-100 dark:text-navy-100 dark:hover:bg-navy-600 dark:focus:bg-navy-600"
                             href="#">
                             <svg xmlns="http://www.w3.org/2000/svg"
                                 class="size-4.5 text-slate-400 transition-colors group-hover:text-slate-500 group-focus:text-slate-500 dark:text-navy-300 dark:group-hover:text-navy-200 dark:group-focus:text-navy-200"
                                 fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="1.5">
                                 <path stroke-linecap="round" stroke-linejoin="round"
                                     d="M11.049 2.927c.3-.921 1.603-.921 1.902 0l1.519 4.674a1 1 0 00.95.69h4.915c.969 0 1.371 1.24.588 1.81l-3.976 2.888a1 1 0 00-.363 1.118l1.518 4.674c.3.922-.755 1.688-1.538 1.118l-3.976-2.888a1 1 0 00-1.176 0l-3.976 2.888c-.783.57-1.838-.197-1.538-1.118l1.518-4.674a1 1 0 00-.363-1.118l-3.976-2.888c-.784-.57-.38-1.81.588-1.81h4.914a1 1 0 00.951-.69l1.519-4.674z" />
                             </svg>
                             <span>Important</span>
                         </a>
                     </li>
                     <li>
                         <a class="group flex space-x-2 rounded-lg p-2 tracking-wide text-slate-800 outline-hidden transition-all hover:bg-slate-100 focus:bg-slate-100 dark:text-navy-100 dark:hover:bg-navy-600 dark:focus:bg-navy-600"
                             href="#">
                             <svg xmlns="http://www.w3.org/2000/svg"
                                 class="size-4.5 text-slate-400 transition-colors group-hover:text-slate-500 group-focus:text-slate-500 dark:text-navy-300 dark:group-hover:text-navy-200 dark:group-focus:text-navy-200"
                                 fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="1.5">
                                 <path stroke-linecap="round" stroke-linejoin="round"
                                     d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6" />
                             </svg>
                             <span>Task</span>
                         </a>
                     </li>
                     <li>
                         <a class="group flex space-x-2 rounded-lg p-2 tracking-wide text-slate-800 outline-hidden transition-all hover:bg-slate-100 focus:bg-slate-100 dark:text-navy-100 dark:hover:bg-navy-600 dark:focus:bg-navy-600"
                             href="#">
                             <svg xmlns="http://www.w3.org/2000/svg"
                                 class="size-4.5 text-slate-400 transition-colors group-hover:text-slate-500 group-focus:text-slate-500 dark:text-navy-300 dark:group-hover:text-navy-200 dark:group-focus:text-navy-200"
                                 fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="1.5">
                                 <path stroke-linecap="round" stroke-linejoin="round"
                                     d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                             </svg>
                             <span>Assigned</span>
                         </a>
                     </li>
                     <li>
                         <a class="group flex space-x-2 rounded-lg p-2 tracking-wide text-error outline-hidden transition-all hover:bg-error/20 focus:bg-error/20"
                             href="#">
                             <svg xmlns="http://www.w3.org/2000/svg" class="size-4.5" fill="none" viewBox="0 0 24 24"
                                 stroke="currentColor" stroke-width="1.5">
                                 <path stroke-linecap="round" stroke-linejoin="round"
                                     d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                             </svg>
                             <span>Deleted</span>
                         </a>
                     </li>
                 </ul>
                 <div class="my-4 mx-4 h-px bg-slate-200 dark:bg-navy-500"></div>
                 <div class="flex items-center justify-between px-4">
                     <span class="text-xs font-medium uppercase">Labels</span>
                     <div class="-mr-1.5 flex">
                         <button
                             class="btn size-6 rounded-full p-0 hover:bg-slate-300/20 focus:bg-slate-300/20 active:bg-slate-300/25 dark:hover:bg-navy-300/20 dark:focus:bg-navy-300/20 dark:active:bg-navy-300/25">
                             <svg xmlns="http://www.w3.org/2000/svg" class="size-3.5" fill="none" viewBox="0 0 24 24"
                                 stroke="currentColor" stroke-width="2">
                                 <path stroke-linecap="round" stroke-linejoin="round"
                                     d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                             </svg>
                         </button>

                         <div x-data="usePopper({ placement: 'bottom-end', offset: 4 })" @click.outside="if(isShowPopper) isShowPopper = false"
                             class="inline-flex">
                             <button x-ref="popperRef" @click="isShowPopper = !isShowPopper"
                                 class="btn size-6 rounded-full p-0 hover:bg-slate-300/20 focus:bg-slate-300/20 active:bg-slate-300/25 dark:hover:bg-navy-300/20 dark:focus:bg-navy-300/20 dark:active:bg-navy-300/25">
                                 <svg xmlns="http://www.w3.org/2000/svg" class="size-3.5" fill="none"
                                     viewBox="0 0 24 24" stroke="currentColor" stroke-width="2">
                                     <path stroke-linecap="round" stroke-linejoin="round"
                                         d="M12 5v.01M12 12v.01M12 19v.01M12 6a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2z" />
                                 </svg>
                             </button>

                             <div x-ref="popperRoot" class="popper-root" :class="isShowPopper && 'show'">
                                 <div
                                     class="popper-box rounded-md border border-slate-150 bg-white py-1.5 font-inter dark:border-navy-500 dark:bg-navy-700">
                                     <ul>
                                         <li>
                                             <a href="#"
                                                 class="flex h-8 items-center px-3 pr-8 font-medium tracking-wide outline-hidden transition-all hover:bg-slate-100 hover:text-slate-800 focus:bg-slate-100 focus:text-slate-800 dark:hover:bg-navy-600 dark:hover:text-navy-100 dark:focus:bg-navy-600 dark:focus:text-navy-100">Action</a>
                                         </li>
                                         <li>
                                             <a href="#"
                                                 class="flex h-8 items-center px-3 pr-8 font-medium tracking-wide outline-hidden transition-all hover:bg-slate-100 hover:text-slate-800 focus:bg-slate-100 focus:text-slate-800 dark:hover:bg-navy-600 dark:hover:text-navy-100 dark:focus:bg-navy-600 dark:focus:text-navy-100">Another
                                                 Action</a>
                                         </li>
                                         <li>
                                             <a href="#"
                                                 class="flex h-8 items-center px-3 pr-8 font-medium tracking-wide outline-hidden transition-all hover:bg-slate-100 hover:text-slate-800 focus:bg-slate-100 focus:text-slate-800 dark:hover:bg-navy-600 dark:hover:text-navy-100 dark:focus:bg-navy-600 dark:focus:text-navy-100">Something
                                                 else</a>
                                         </li>
                                     </ul>
                                     <div class="my-1 h-px bg-slate-150 dark:bg-navy-500"></div>
                                     <ul>
                                         <li>
                                             <a href="#"
                                                 class="flex h-8 items-center px-3 pr-8 font-medium tracking-wide outline-hidden transition-all hover:bg-slate-100 hover:text-slate-800 focus:bg-slate-100 focus:text-slate-800 dark:hover:bg-navy-600 dark:hover:text-navy-100 dark:focus:bg-navy-600 dark:focus:text-navy-100">Separated
                                                 Link</a>
                                         </li>
                                     </ul>
                                 </div>
                             </div>
                         </div>
                     </div>
                 </div>
                 <ul class="mt-1 space-y-1.5 px-2 font-inter text-xs-plus font-medium">
                     <li>
                         <a class="group flex space-x-2 rounded-lg p-2 tracking-wide outline-hidden transition-all hover:bg-success/20 focus:bg-success/20"
                             href="#">
                             <svg class="size-4.5 text-success" stroke="currentColor" viewBox="0 0 24 24"
                                 stroke-width="1.5" fill="none" xmlns="http://www.w3.org/2000/svg">
                                 <path d="M7 6H21M7 12H21M7 18H21" stroke-linecap="round" stroke-linejoin="round" />
                                 <path d="M3 6H4M3 12H4M3 18H4" stroke-linecap="round" stroke-linejoin="round" />
                             </svg>
                             <span class="text-slate-800 dark:text-navy-100">Low</span>
                         </a>
                     </li>
                     <li>
                         <a class="group flex space-x-2 rounded-lg p-2 tracking-wide outline-hidden transition-all hover:bg-warning/20 focus:bg-warning/20"
                             href="#">
                             <svg class="size-4.5 text-warning" stroke="currentColor" viewBox="0 0 24 24"
                                 stroke-width="1.5" fill="none" xmlns="http://www.w3.org/2000/svg">
                                 <path d="M7 6H21M7 12H21M7 18H21" stroke-linecap="round" stroke-linejoin="round" />
                                 <path d="M3 6H4M3 12H4M3 18H4" stroke-linecap="round" stroke-linejoin="round" />
                             </svg>
                             <span class="text-slate-800 dark:text-navy-100">Medium</span>
                         </a>
                     </li>
                     <li>
                         <a class="group flex space-x-2 rounded-lg p-2 tracking-wide outline-hidden transition-all hover:bg-error/20 focus:bg-error/20"
                             href="#">
                             <svg class="size-4.5 text-error" stroke="currentColor" viewBox="0 0 24 24"
                                 stroke-width="1.5" fill="none" xmlns="http://www.w3.org/2000/svg">
                                 <path d="M7 6H21M7 12H21M7 18H21" stroke-linecap="round" stroke-linejoin="round" />
                                 <path d="M3 6H4M3 12H4M3 18H4" stroke-linecap="round" stroke-linejoin="round" />
                             </svg>
                             <span class="text-slate-800 dark:text-navy-100">High</span>
                         </a>
                     </li>
                     <li>
                         <a class="group flex space-x-2 rounded-lg p-2 tracking-wide outline-hidden transition-all hover:bg-info/20 focus:bg-info/20"
                             href="#">
                             <svg class="size-4.5 text-info" stroke="currentColor" viewBox="0 0 24 24"
                                 stroke-width="1.5" fill="none" xmlns="http://www.w3.org/2000/svg">
                                 <path d="M7 6H21M7 12H21M7 18H21" stroke-linecap="round" stroke-linejoin="round" />
                                 <path d="M3 6H4M3 12H4M3 18H4" stroke-linecap="round" stroke-linejoin="round" />
                             </svg>
                             <span class="text-slate-800 dark:text-navy-100">Update</span>
                         </a>
                     </li>
                 </ul>
             </div>

             <div class="flex shrink-0 justify-between px-1.5 py-1">
                 <a href="" x-tooltip="'Mail App'"
                     class="btn size-9 rounded-full p-0 hover:bg-slate-300/20 focus:bg-slate-300/20 active:bg-slate-300/25 dark:hover:bg-navy-300/20 dark:focus:bg-navy-300/20 dark:active:bg-navy-300/25">
                     <svg xmlns="http://www.w3.org/2000/svg" class="size-5" fill="none" viewBox="0 0 24 24"
                         stroke="currentColor" stroke-width="1.5">
                         <path stroke-linecap="round" stroke-linejoin="round"
                             d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                     </svg>
                 </a>
                 <a href="" x-tooltip="'Kanban App'"
                     class="btn size-9 rounded-full p-0 hover:bg-slate-300/20 focus:bg-slate-300/20 active:bg-slate-300/25 dark:hover:bg-navy-300/20 dark:focus:bg-navy-300/20 dark:active:bg-navy-300/25">
                     <svg xmlns="http://www.w3.org/2000/svg" class="size-5" fill="none" viewBox="0 0 24 24"
                         stroke="currentColor" stroke-width="1.5">
                         <path stroke-linecap="round" stroke-linejoin="round"
                             d="M9 17V7m0 10a2 2 0 01-2 2H5a2 2 0 01-2-2V7a2 2 0 012-2h2a2 2 0 012 2m0 10a2 2 0 002 2h2a2 2 0 002-2M9 7a2 2 0 012-2h2a2 2 0 012 2m0 10V7m0 10a2 2 0 002 2h2a2 2 0 002-2V7a2 2 0 00-2-2h-2a2 2 0 00-2 2" />
                     </svg>
                 </a>
                 <a href="" x-tooltip="'Chat App'"
                     class="btn size-9 rounded-full p-0 hover:bg-slate-300/20 focus:bg-slate-300/20 active:bg-slate-300/25 dark:hover:bg-navy-300/20 dark:focus:bg-navy-300/20 dark:active:bg-navy-300/25">
                     <svg xmlns="http://www.w3.org/2000/svg" class="size-5" fill="none" viewBox="0 0 24 24"
                         stroke="currentColor" stroke-width="1.5">
                         <path stroke-linecap="round" stroke-linejoin="round"
                             d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
                     </svg>
                 </a>
                 <a href="" x-tooltip="'POS App'"
                     class="btn size-9 rounded-full p-0 hover:bg-slate-300/20 focus:bg-slate-300/20 active:bg-slate-300/25 dark:hover:bg-navy-300/20 dark:focus:bg-navy-300/20 dark:active:bg-navy-300/25">
                     <svg xmlns="http://www.w3.org/2000/svg" class="size-5" fill="none" viewBox="0 0 24 24"
                         stroke="currentColor" stroke-width="1.5">
                         <path stroke-linecap="round" stroke-linejoin="round"
                             d="M17 9V7a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2m2 4h10a2 2 0 002-2v-6a2 2 0 00-2-2H9a2 2 0 00-2 2v6a2 2 0 002 2zm7-5a2 2 0 11-4 0 2 2 0 014 0z" />
                     </svg>
                 </a>
                 <a href="" x-tooltip="'File Manager App'"
                     class="btn size-9 rounded-full p-0 hover:bg-slate-300/20 focus:bg-slate-300/20 active:bg-slate-300/25 dark:hover:bg-navy-300/20 dark:focus:bg-navy-300/20 dark:active:bg-navy-300/25">
                     <svg xmlns="http://www.w3.org/2000/svg" class="size-5" fill="none" viewBox="0 0 24 24"
                         stroke="currentColor" stroke-width="1.5">
                         <path stroke-linecap="round" stroke-linejoin="round"
                             d="M8 7v8a2 2 0 002 2h6M8 7V5a2 2 0 012-2h4.586a1 1 0 01.707.293l4.414 4.414a1 1 0 01.293.707V15a2 2 0 01-2 2h-2M8 7H6a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2v-2" />
                     </svg>
                 </a>
             </div>
         </div>
     </div>
 </div>
<?php /**PATH J:\Laravel_Projects\alhars_last-Rev\resources\views/livewire/appointments/appointments-sidebar.blade.php ENDPATH**/ ?>