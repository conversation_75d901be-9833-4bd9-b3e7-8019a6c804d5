<?php if (isset($component)) { $__componentOriginaldd14381f5120c401c6bcd3f0718fe05c = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginaldd14381f5120c401c6bcd3f0718fe05c = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.base-layout','data' => ['title' => 'Appointments','isSidebarOpen' => 'true','isHeaderBlur' => 'true','hasMinSidebar' => 'true']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('base-layout'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['title' => 'Appointments','is-sidebar-open' => 'true','is-header-blur' => 'true','has-min-sidebar' => 'true']); ?>
    <?php
$__split = function ($name, $params = []) {
    return [$name, $params];
};
[$__name, $__params] = $__split('appointments.appointments', []);

$__html = app('livewire')->mount($__name, $__params, 'lw-3229008911-0', $__slots ?? [], get_defined_vars());

echo $__html;

unset($__html);
unset($__name);
unset($__params);
unset($__split);
if (isset($__slots)) unset($__slots);
?>
 <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginaldd14381f5120c401c6bcd3f0718fe05c)): ?>
<?php $attributes = $__attributesOriginaldd14381f5120c401c6bcd3f0718fe05c; ?>
<?php unset($__attributesOriginaldd14381f5120c401c6bcd3f0718fe05c); ?>
<?php endif; ?>
<?php if (isset($__componentOriginaldd14381f5120c401c6bcd3f0718fe05c)): ?>
<?php $component = $__componentOriginaldd14381f5120c401c6bcd3f0718fe05c; ?>
<?php unset($__componentOriginaldd14381f5120c401c6bcd3f0718fe05c); ?>
<?php endif; ?>
<?php /**PATH J:\Laravel_Projects\alhars_last-Rev\resources\views/pages/appointments.blade.php ENDPATH**/ ?>