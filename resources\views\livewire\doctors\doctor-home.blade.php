<div class="contents">
    <main class="main-content w-full px-[var(--margin-x)] pb-8">
        <div class="mt-4 grid grid-cols-12 gap-4 sm:mt-5 sm:gap-5 lg:mt-6 lg:gap-6">
            <div class="col-span-12 lg:col-span-8 xl:col-span-9">
                <div
                    class="card col-span-12 mt-12 bg-linear-to-r from-blue-500 to-blue-600 p-5 sm:col-span-8 sm:mt-0 sm:flex-row">
                    <div class="flex justify-center sm:order-last">
                        <img class="-mt-16 h-40 sm:mt-0" src="{{ asset('images/illustrations/doctor.svg') }}"
                            alt="image" />
                    </div>
                    <div class="mt-2 flex-1 pt-2 text-center text-white sm:mt-0 sm:text-left">
                        <h3 class="text-xl">
                            <span class="font-semibold">Dr. {{ $doctor->name }}</span>
                        </h3>

                        <p class="mt-2 leading-relaxed">{{ $doctor->specialization }}</p>
                        <p>{{ $doctor->experience }}</p>

                        <label class="mt-9 inline-flex items-center space-x-2">
                            <input
                                class="form-switch is-outline h-5 w-10 rounded-full border border-slate-400/70 bg-slate-100 before:rounded-full before:bg-slate-300 checked:!border-info checked:before:!bg-info dark:border-navy-500 dark:bg-navy-900 dark:before:bg-navy-400"
                                type="checkbox" {{ $doctor->is_available ? 'checked' : '' }}
                                wire:click="switchDoctorOnAndOff({{ $doctor->id }}, {{ json_encode($doctor->is_available) }})" />
                            <span>{{ $doctor->is_available ? 'Available' : 'Not Available' }}</span>
                        </label>
                    </div>
                </div>

                <div class="mt-4 sm:mt-5 lg:mt-6">
                    <div class="flex h-8 items-center justify-between">
                        <h2 class="text-base font-medium tracking-wide text-slate-700 dark:text-navy-100">
                            Working Days
                        </h2>
                    </div>
                    <div class="mt-3 grid grid-cols-1 gap-4 sm:grid-cols-{{ count($doctor->working_days) }} sm:gap-5">
                        @foreach ($doctor->working_days as $workingDay)
                            <div class="rounded-lg bg-gradient-to-r from-green-400 to-fuchsia-400 p-0.5">
                                <div class="rounded-lg bg-slate-50 px-1 py-1 dark:bg-navy-900 sm:px-5">
                                    <div>
                                        <h2
                                            class="text-lg text-center font-medium tracking-wide text-slate-700 line-clamp-1 dark:text-navy-100">
                                            {{ $workingDay }}
                                        </h2>
                                    </div>
                                </div>
                            </div>
                        @endforeach
                    </div>
                </div>

                <div class="mt-4 sm:mt-5 lg:mt-6">
                    <div class="flex items-center justify-between">
                        <h2 class="text-base font-medium tracking-wide text-slate-700 line-clamp-1 dark:text-navy-100">
                            Appointments
                        </h2>
                        <div class="flex">
                            <div class="flex items-center" x-data="{ isInputActive: false }">
                                <label class="block">
                                    <input x-effect="isInputActive === true && $nextTick(() => { $el.focus()});"
                                        :class="isInputActive ? 'w-32 lg:w-48' : 'w-0'"
                                        class="form-input bg-transparent px-1 text-right transition-all duration-100 placeholder:text-slate-500 dark:placeholder:text-navy-200"
                                        placeholder="Search here..." type="text" />
                                </label>
                                <button @click="isInputActive = !isInputActive"
                                    class="btn size-8 rounded-full p-0 hover:bg-slate-300/20 focus:bg-slate-300/20 active:bg-slate-300/25 dark:hover:bg-navy-300/20 dark:focus:bg-navy-300/20 dark:active:bg-navy-300/25">
                                    <svg xmlns="http://www.w3.org/2000/svg" class="size-4.5" fill="none"
                                        viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5"
                                            d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                                    </svg>
                                </button>
                            </div>
                            <div x-data="usePopper({ placement: 'bottom-end', offset: 4 })" @click.outside="if(isShowPopper) isShowPopper = false"
                                class="inline-flex">
                                <button x-ref="popperRef" @click="isShowPopper = !isShowPopper"
                                    class="btn size-8 rounded-full p-0 hover:bg-slate-300/20 focus:bg-slate-300/20 active:bg-slate-300/25 dark:hover:bg-navy-300/20 dark:focus:bg-navy-300/20 dark:active:bg-navy-300/25">
                                    <svg xmlns="http://www.w3.org/2000/svg" class="size-4.5" fill="none"
                                        viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                            d="M12 5v.01M12 12v.01M12 19v.01M12 6a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2z" />
                                    </svg>
                                </button>
                                <div x-ref="popperRoot" class="popper-root" :class="isShowPopper && 'show'">
                                    <div
                                        class="popper-box rounded-md border border-slate-150 bg-white py-1.5 font-inter dark:border-navy-500 dark:bg-navy-700">
                                        <ul>
                                            <li>
                                                <a href="#"
                                                    class="flex h-8 items-center px-3 pr-8 font-medium tracking-wide outline-hidden transition-all hover:bg-slate-100 hover:text-slate-800 focus:bg-slate-100 focus:text-slate-800 dark:hover:bg-navy-600 dark:hover:text-navy-100 dark:focus:bg-navy-600 dark:focus:text-navy-100">Action</a>
                                            </li>
                                            <li>
                                                <a href="#"
                                                    class="flex h-8 items-center px-3 pr-8 font-medium tracking-wide outline-hidden transition-all hover:bg-slate-100 hover:text-slate-800 focus:bg-slate-100 focus:text-slate-800 dark:hover:bg-navy-600 dark:hover:text-navy-100 dark:focus:bg-navy-600 dark:focus:text-navy-100">Another
                                                    Action</a>
                                            </li>
                                            <li>
                                                <a href="#"
                                                    class="flex h-8 items-center px-3 pr-8 font-medium tracking-wide outline-hidden transition-all hover:bg-slate-100 hover:text-slate-800 focus:bg-slate-100 focus:text-slate-800 dark:hover:bg-navy-600 dark:hover:text-navy-100 dark:focus:bg-navy-600 dark:focus:text-navy-100">Something
                                                    else</a>
                                            </li>
                                        </ul>
                                        <div class="my-1 h-px bg-slate-150 dark:bg-navy-500"></div>
                                        <ul>
                                            <li>
                                                <a href="#"
                                                    class="flex h-8 items-center px-3 pr-8 font-medium tracking-wide outline-hidden transition-all hover:bg-slate-100 hover:text-slate-800 focus:bg-slate-100 focus:text-slate-800 dark:hover:bg-navy-600 dark:hover:text-navy-100 dark:focus:bg-navy-600 dark:focus:text-navy-100">Separated
                                                    Link</a>
                                            </li>
                                        </ul>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="card mt-3">
                        <div class="is-scrollbar-hidden min-w-full overflow-x-auto" style="min-height: 500px">
                            <table class="is-hoverable w-full text-left">
                                <thead>
                                    <tr>
                                        <th
                                            class="whitespace-nowrap rounded-tl-lg bg-slate-200 px-4 py-3 font-semibold uppercase text-slate-800 dark:bg-navy-800 dark:text-navy-100 lg:px-5">
                                            NAME
                                        </th>
                                        <th
                                            class="whitespace-nowrap bg-slate-200 px-4 py-3 font-semibold uppercase text-slate-800 dark:bg-navy-800 dark:text-navy-100 lg:px-5">
                                            SERVICE
                                        </th>
                                        <th
                                            class="whitespace-nowrap bg-slate-200 px-4 py-3 font-semibold uppercase text-slate-800 dark:bg-navy-800 dark:text-navy-100 lg:px-5">
                                            DATE & TIME
                                        </th>
                                        <th
                                            class="whitespace-nowrap bg-slate-200 px-4 py-3 font-semibold uppercase text-slate-800 dark:bg-navy-800 dark:text-navy-100 lg:px-5 w-10">
                                            STATUS
                                        </th>

                                        <th
                                            class="whitespace-nowrap bg-slate-200 px-4 py-3 font-semibold uppercase text-slate-800 dark:bg-navy-800 dark:text-navy-100 lg:px-5 w-10">
                                            ATTENDED
                                        </th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @if ($appointments->count() > 0)
                                        @foreach ($appointments as $appointment)
                                            <tr
                                                class="border-y border-transparent border-b-slate-200 dark:border-b-navy-500">
                                                <td class="whitespace-nowrap px-4 py-3 sm:px-5">
                                                    <div class="flex items-center space-x-4">
                                                        <div class="avatar size-9">
                                                            @if ($appointment->customer->avatar_url)
                                                                <img class="rounded-full"
                                                                    src="{{ asset('storage/customers/' . $appointment->customer->avatar_url) }}"
                                                                    alt="avatar" />
                                                            @else
                                                                <div
                                                                    class="is-initial rounded-full bg-slate-200 text-l uppercase text-slate-600 dark:bg-navy-500 dark:text-navy-100">
                                                                    {{ mb_substr($appointment->customer->name, 0, 1) }}
                                                                </div>
                                                            @endif

                                                        </div>

                                                        <div class="flex flex-col">
                                                            <span class="font-medium text-slate-700 dark:text-navy-100">
                                                                {{ $appointment->customer->name }}
                                                            </span>
                                                            <p class="text-xs mt-2 text-slate-400 dark:text-navy-300">
                                                                {{ $appointment->customer->phone_number }}
                                                            </p>
                                                        </div>
                                                    </div>
                                                </td>
                                                <td class="whitespace-nowrap px-4 py-3 sm:px-5">
                                                    <a href="#" class="hover:underline focus:underline">
                                                        {{ $appointment->service?->name }}
                                                    </a>
                                                </td>
                                                <td
                                                    class="whitespace-nowrap px-4 py-3 font-medium text-slate-600 dark:text-navy-100 sm:px-5">
                                                    {{ $appointment->date }}
                                                </td>
                                                <td class="whitespace-nowrap px-4 py-3 sm:px-5 w-10">
                                                    <div class="flex items-center justify-center">
                                                        @if ($appointment->status === 'confirmed' || $appointment->status === 'attended')
                                                            <svg xmlns="http://www.w3.org/2000/svg" class="size-5 text-success"
                                                                fill="none" viewBox="0 0 24 24"
                                                                stroke="currentColor">
                                                                <path stroke-linecap="round" stroke-linejoin="round"
                                                                    stroke-width="1.5"
                                                                    d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                                                            </svg>
                                                        @elseif ($appointment->status === 'pending')
                                                            <svg xmlns="http://www.w3.org/2000/svg" class="size-5 text-warning"
                                                                fill="none" viewBox="0 0 24 24"
                                                                stroke="currentColor">
                                                                <path stroke-linecap="round" stroke-linejoin="round"
                                                                    stroke-width="1.5"
                                                                    d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                                                            </svg>
                                                        @else
                                                            <svg xmlns="http://www.w3.org/2000/svg"
                                                                class="size-5 text-error" fill="none"
                                                                viewBox="0 0 24 24" stroke="currentColor">
                                                                <path stroke-linecap="round" stroke-linejoin="round"
                                                                    stroke-width="1.5"
                                                                    d="M10 14l2-2m0 0l2-2m-2 2l-2-2m2 2l2 2m7-2a9 9 0 11-18 0 9 9 0 0118 0z">
                                                                </path>
                                                            </svg>
                                                        @endif
                                                    </div>
                                                </td>

                                                <td class="whitespace-nowrap px-4 py-3 sm:px-5 w-10">
                                                    <div class="flex items-center justify-center">
                                                        @if ($appointment->attended)
                                                            <svg xmlns="http://www.w3.org/2000/svg"
                                                                class="size-5 text-success" fill="none"
                                                                viewBox="0 0 24 24" stroke="currentColor">
                                                                <path stroke-linecap="round" stroke-linejoin="round"
                                                                    stroke-width="1.5"
                                                                    d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                                                            </svg>
                                                        @else
                                                            <svg xmlns="http://www.w3.org/2000/svg"
                                                                class="size-5 text-warning" fill="none"
                                                                viewBox="0 0 24 24" stroke="currentColor">
                                                                <path stroke-linecap="round" stroke-linejoin="round"
                                                                    stroke-width="1.5"
                                                                    d="M10 14l2-2m0 0l2-2m-2 2l-2-2m2 2l2 2m7-2a9 9 0 11-18 0 9 9 0 0118 0z">
                                                                </path>
                                                            </svg>
                                                        @endif
                                                    </div>
                                                </td>

                                            </tr>
                                        @endforeach
                                    @else
                                        <tr>
                                            <td colspan="5">
                                                <div
                                                    class="flex items-center justify-center h-[500px] text-slate-500 dark:text-navy-100">
                                                    <svg xmlns="http://www.w3.org/2000/svg" class="size-12"
                                                        fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                        <path stroke-linecap="round" stroke-linejoin="round"
                                                            stroke-width="1.5"
                                                            d="M8 10h.01M12 10h.01M16 10h.01M9 16H5a2 2 0 01-2-2V6a2 2 0 012-2h14a2 2 0 012 2v8a2 2 0 01-2 2h-4m-4 4h.01M12 14h.01M16 14h.01" />
                                                    </svg>
                                                    No Appointments Yet
                                                </div>
                                            </td>
                                        </tr>
                                    @endif
                                </tbody>
                            </table>
                            {{ $appointments->links('components.pagination1') }}
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-span-12 lg:col-span-4 xl:col-span-3">
                <div class="grid grid-cols-1 gap-4 sm:grid-cols-2 sm:gap-5 lg:grid-cols-1 lg:gap-6">
                    <div class="rounded-lg bg-info/10 px-4 pb-5 dark:bg-navy-800 sm:px-5">
                        <div class="flex items-center justify-between py-3">
                            <h2 class="font-medium tracking-wide text-slate-700 dark:text-navy-100">
                                Doctor Information
                            </h2>
                        </div>
                        <div class="space-y-4">
                            <div class="flex justify-between">
                                <div class="avatar size-16 relative">
                                    @if ($doctor->avatar_url)
                                        <img class="rounded-full"
                                            src="{{ asset('storage/doctors/' . $doctor->avatar_url) }}"
                                            alt="image" />
                                    @else
                                        <div
                                            class="is-initial rounded-full bg-slate-200 text-xl uppercase text-slate-600 dark:bg-navy-500 dark:text-navy-100">
                                            {{ mb_substr($doctor->name, 0, 2) }}
                                        </div>
                                        @if ($doctor->shift === now()->format('A'))
                                            <div
                                                class="absolute right-0 size-3.5 rounded-full border-2 border-white bg-success dark:border-navy-700">
                                                <span
                                                    class="absolute inline-flex h-full w-full animate-ping rounded-full bg-success opacity-80"></span>
                                            </div>
                                        @endif

                                    @endif

                                </div>
                                <div>
                                    <p>Shift</p>
                                    <p class="text-xl font-medium text-slate-700 dark:text-navy-100">
                                        {{ $doctor->shift }}
                                    </p>
                                </div>
                            </div>
                            <div>
                                <h3 class="text-lg font-medium text-slate-700 dark:text-navy-100">
                                    {{ $doctor->name }}
                                </h3>
                                <p class="text-xs text-slate-400 dark:text-navy-300">
                                    {{ $doctor->specialization }}
                                </p>
                            </div>
                            <div class="my-4 h-px  bg-slate-200 dark:bg-navy-500"></div>
                            <div class="space-y-3 text-xs-plus">
                                <div class="flex justify-between">
                                    <p class="font-medium text-slate-700 dark:text-navy-100">
                                        Education
                                    </p>
                                    <p class="text-right">{{ $doctor->education }}</p>
                                </div>
                                <div class="my-4 h-px  bg-slate-200 dark:bg-navy-500"></div>
                                <div class="flex justify-between">
                                    <p class="font-medium text-slate-700 dark:text-navy-100">
                                        Phone
                                    </p>
                                    <p class="text-right">{{ $doctor->phone_number ?? 'N/A' }}</p>
                                </div>
                                <div class="my-4 h-px  bg-slate-200 dark:bg-navy-500"></div>
                                <div class="flex justify-between">
                                    <p class="font-medium text-slate-700 dark:text-navy-100">
                                        Experience
                                    </p>
                                    <p class="text-right">{{ $doctor->experience }}</p>
                                </div>
                                <div class="my-4 h-px  bg-slate-200 dark:bg-navy-500"></div>
                                <div class="flex justify-between">
                                    <p class="font-medium text-slate-700 dark:text-navy-100">
                                        Spoken Languages
                                    </p>
                                    <p class="text-right">{{ implode(', ', $doctor->languages) }}</p>
                                </div>
                                <div class="my-4 h-px  bg-slate-200 dark:bg-navy-500"></div>
                                <div class="flex justify-between">
                                    <p class="font-medium text-slate-700 dark:text-navy-100">
                                        Last Appointment
                                    </p>
                                    <p class="text-right">
                                        {{ $appointments->first()?->appointment_date->format('d M Y') ?? 'N/A' }}</p>
                                </div>
                                <div class="my-4 h-px  bg-slate-200 dark:bg-navy-500"></div>
                                <div class="flex justify-between">
                                    <p class="font-medium text-slate-700 dark:text-navy-100">
                                        Joined Date
                                    </p>
                                    <p class="text-right">{{ $doctor->created_at->format('d M Y') }}</p>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="card sm:order-last sm:col-span-2 lg:order-none lg:col-span-1">
                        <div class="mt-3 flex items-center justify-between px-4 sm:px-5">
                            <h2 class="font-medium tracking-wide text-slate-700 dark:text-navy-100">
                                Certificates
                            </h2>
                        </div>
                        <div class="space-y-4 p-4 sm:p-5">
                            @foreach ($doctor->certifications as $certification)
                                <div
                                    class="rounded-lg bg-gradient-to-br from-purple-500 to-indigo-600 px-4 py-4 text-white sm:px-5">
                                    <div class="flex items-center space-x-4">
                                        <img class="size-9" src="{{ asset('images/awards/award-1.svg') }}"
                                            alt="avatar">
                                        <div>
                                            <h3 class="font-medium text-slate-700 line-clamp-1 dark:text-navy-100">
                                                {{ $certification }}
                                            </h3>
                                        </div>
                                    </div>
                                </div>
                            @endforeach
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </main>
</div>
