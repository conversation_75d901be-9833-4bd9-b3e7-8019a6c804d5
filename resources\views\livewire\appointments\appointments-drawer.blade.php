    <div x-show="showDrawer" x-data="{
        showDrawer: false,
        clinics: @entangle('clinics'),
        selectedClinic_id: null,
        doctors: [],
        services: [],
        selectedAppointment: null,

        optionsStyle: {
            option: (data, escape) => {
                if (data.value === 'loading') {
                    return `
                                                                                                                                                                                            <div style='
                                                                                                                                                                                              display: flex; 
                                                                                                                                                                                              justify-content: center; 
                                                                                                                                                                                              align-items: center; 
                                                                                                                                                                                              height: calc(2 * 38px);
                                                                                                                                                                                            '>
                                                                                                                                                                                              <span style='
                                                                                                                                                                                                border: 3px solid #ccc; 
                                                                                                                                                                                                border-top: 3px solid #333; 
                                                                                                                                                                                                border-radius: 50%; 
                                                                                                                                                                                                width: 20px; 
                                                                                                                                                                                                height: 20px; 
                                                                                                                                                                                                animation: spin 0.5s linear infinite;
                                                                                                                                                                                                margin-right: 10px;
                                                                                                                                                                                              '></span>
                                                                                                                                                                                              ${escape(data.text)}
                                                                                                                                                                                            </div>`;
                }
                return `<div>${escape(data.text)}</div>`;
            }
        },
    
    }"
        x-on:show-drawer.window="($event.detail.drawerId === 'appointments-drawer') && (showDrawer = true)"
        x-on:appointment-selected.window="selectedAppointment = $event.detail"
        @keydown.window.escape="showDrawer = false"
        <div class="fixed inset-0 z-100 bg-slate-900/60 transition-opacity duration-200" @click="showDrawer = false"
            x-show="showDrawer" x-transition:enter="ease-out" x-transition:enter-start="opacity-0"
            x-transition:enter-end="opacity-100" x-transition:leave="ease-in" x-transition:leave-start="opacity-100"
            x-transition:leave-end="opacity-0"></div>
        <template x-if="selectedAppointment">
            <div class="fixed right-0 top-0 z-101 h-full w-80">
                <div class="flex h-full w-full transform-gpu flex-col bg-white transition-transform duration-200 dark:bg-navy-700"
                    x-show="showDrawer" x-transition:enter="ease-out" x-transition:enter-start="translate-x-full"
                    x-transition:enter-end="translate-x-0" x-transition:leave="ease-in"
                    x-transition:leave-start="translate-x-0" x-transition:leave-end="translate-x-full">
                    <div class="flex h-14 items-center justify-between bg-slate-150 p-4 dark:bg-navy-800">
                        <h3 class="text-base font-medium text-slate-700 dark:text-navy-100">
                            More Details
                        </h3>
                        <div class="-mr-1.5 flex items-center space-x-2.5">
                            <input x-tooltip.primary="'Mark as Attended'"
                                x-effect="showDrawer && setTimeout(() => showDrawer && $el.__x_tippy.show(), 500)"
                                class="form-checkbox is-basic size-5 rounded-full border-slate-400/70 checked:border-primary checked:bg-primary hover:border-primary focus:border-primary dark:border-navy-400 dark:checked:border-accent dark:checked:bg-accent dark:hover:border-accent dark:focus:border-accent"
                                type="checkbox" />
                            <div class="flex">
                                <button x-data="{ isImportant: false }" @click.stop="isImportant =! isImportant"
                                    class="btn size-7 rounded-full p-0 hover:bg-slate-300/20 focus:bg-slate-300/20 active:bg-slate-300/25 dark:hover:bg-navy-300/20 dark:focus:bg-navy-300/20 dark:active:bg-navy-300/25">
                                    <svg x-show="!isImportant" xmlns="http://www.w3.org/2000/svg" class="size-4.5"
                                        fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5"
                                            d="M11.049 2.927c.3-.921 1.603-.921 1.902 0l1.519 4.674a1 1 0 00.95.69h4.915c.969 0 1.371 1.24.588 1.81l-3.976 2.888a1 1 0 00-.363 1.118l1.518 4.674c.3.922-.755 1.688-1.538 1.118l-3.976-2.888a1 1 0 00-1.176 0l-3.976 2.888c-.783.57-1.838-.197-1.538-1.118l1.518-4.674a1 1 0 00-.363-1.118l-3.976-2.888c-.784-.57-.38-1.81.588-1.81h4.914a1 1 0 00.951-.69l1.519-4.674z" />
                                    </svg>
                                    <svg x-show="isImportant" xmlns="http://www.w3.org/2000/svg"
                                        class="size-5.5 text-primary dark:text-accent" viewBox="0 0 20 20"
                                        fill="currentColor" style="display: none">
                                        <path
                                            d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                                    </svg>
                                </button>
                                <button @click="showDrawer=false"
                                    class="btn size-7 rounded-full p-0 hover:bg-slate-300/20 focus:bg-slate-300/20 active:bg-slate-300/25 dark:hover:bg-navy-300/20 dark:focus:bg-navy-300/20 dark:active:bg-navy-300/25">
                                    <svg xmlns="http://www.w3.org/2000/svg" class="size-4.5" fill="none"
                                        viewBox="0 0 24 24" stroke="currentColor" stroke-width="2">
                                        <path stroke-linecap="round" stroke-linejoin="round" d="M6 18L18 6M6 6l12 12" />
                                    </svg>
                                </button>
                            </div>
                        </div>
                    </div>
                    <div class="is-scrollbar-hidden flex grow flex-col space-y-4 overflow-y-auto p-4">
                        <label wire:ignore class="block">
                            <span>Clinic</span>
                            <select class="w-full mt-1.5" x-init="const selected = {
                                value: selectedAppointment?.clinic?.id,
                                text: selectedAppointment?.clinic?.name,
                            };
                            const tom = new Tom($el, {
                                searchField: ['text'],
                                create: false,
                                placeholder: 'Search and select clinic...',
                                options: [selected],
                                onChange: function(val) {
                                    selectedAppointment.clinic_id = val;
                                    if (clinics.length !== 0) {
                                        doctors = clinics.filter(c => c.id.toString() === val.toString())[0].doctors;
                                        services = clinics.filter(c => c.id.toString() === val.toString())[0].services;
                                        selectedAppointment.doctor_id = doctors[0].id;
                                        selectedAppointment.service_id = services[0].id;
                                    }
                                },
                                onDropdownOpen: async function() {
                                    this.clearOptions();

                                    this.addOption({ value: 'loading', text: 'Loading Clinics...', disabled: true });
                            
                                    if (clinics.length === 0) {
                                        await @this.call('getClinics');
                                    }
                            
                                    this.removeOption('loading');
                            
                                    
                                    if (clinics?.length) {
                                        clinics.forEach(c => {
                                            this.addOption({ value: c.id, text: c.name });
                                        });
                                        this.refreshOptions(false);
                                    }
                                },
                                render: optionsStyle,
                            
                            });
                            tom.setValue(selected.value);
                            $el._tom = tom;">
                            </select>
                        </label>

                        <label wire:ignore class="block">
                            <span>Doctor</span>
                            <select class="w-full mt-1.5" x-init="const selected = {
                                value: selectedAppointment?.doctor?.id,
                                text: selectedAppointment?.doctor?.name,
                            };
                            const tom = new Tom($el, {
                                searchField: ['text'],
                                create: false,
                                placeholder: 'Search and select doctor...',
                                options: [selected],
                                onChange: function(val) {
                                    selectedAppointment.doctor_id = val;
                                },
                            
                                onDropdownOpen: async function() {
                                    this.clearOptions();
                            
                                    this.addOption({ value: 'loading', text: 'Loading Doctors...', disabled: true });
                            
                                    if (clinics.length === 0) {
                                        await @this.call('getClinics');
                                    }
                            
                                    this.removeOption('loading');
                            
                                    if (clinics.length !== 0) {
                                        doctors = clinics.filter(c => c.id.toString() === selectedAppointment.clinic_id.toString())[0].doctors;
                                        doctors.forEach(d => this.addOption({ value: d.id, text: d.name }));
                                        this.refreshOptions(false);
                                    }
                            
                                    if (doctors.length === 0) {
                                        this.addOption({ value: 'no-options', text: 'No Doctors Found', disabled: true });
                                    }
                            
                                },
                            
                                render: optionsStyle,
                            
                            });
                            tom.setValue(selected.value);
                            $el._tom = tom;"
                                x-effect="
                                if (doctors.length !== 0 && selectedAppointment?.doctor_id !== $el._tom.getValue()) {
                                    $el._tom.clearOptions();
                                    doctors.forEach(d => $el._tom.addOption({ value: d.id, text: d.name }));
                                    $el._tom.setValue(selectedAppointment?.doctor_id);
                                    }else if (doctors.length === 0) {
                                    $el._tom.addOption({ value: 'no-options', text: 'No Doctors Found', disabled: true });
                                    }
                                ">
                            </select>
                        </label>

                        <label wire:ignore class="block">
                            <span>Service</span>
                            <select class="w-full mt-1.5" x-init="const selected = {
                                value: selectedAppointment?.service?.id,
                                text: selectedAppointment?.service?.name,
                            };
                            const tom = new Tom($el, {
                                searchField: ['text'],
                                create: false,
                                placeholder: 'Search and select service...',
                                options: [selected],
                                onChange: function(val) {
                                    selectedAppointment.service_id = val;
                                },
                            
                                onDropdownOpen: async function() {
                                    this.clearOptions();
                            
                                    this.addOption({ value: 'loading', text: 'Loading Services...', disabled: true });
                            
                                    if (clinics.length === 0) {
                                        await @this.call('getClinics');
                                    }
                            
                                    this.removeOption('loading');
                            
                                    if (clinics.length !== 0) {
                                        services = clinics.filter(c => c.id.toString() === selectedAppointment.clinic_id.toString())[0].services;
                                        services.forEach(d => this.addOption({ value: d.id, text: d.name }));
                                        this.refreshOptions(false);
                                    }
                            
                                    if (services.length === 0) {
                                        this.addOption({ value: 'no-options', text: 'No Services Found', disabled: true });
                                    }
                                },
                            
                                render: optionsStyle,
                            
                            });
                            tom.setValue(selected.value);
                            $el._tom = tom;"
                                x-effect="
                                if (services.length !== 0 && selectedAppointment?.service_id !== $el._tom.getValue()) {
                                    $el._tom.clearOptions();
                                    services.forEach(d => $el._tom.addOption({ value: d.id, text: d.name }));
                                    $el._tom.setValue(selectedAppointment?.service_id);
                                    }else if (services.length === 0) {
                                    $el._tom.addOption({ value: 'no-options', text: 'No Services Found', disabled: true });
                                    }
                                ">
                            </select>
                        </label>

                        <div>
                            <span>Date:</span>
                            <label class="relative mt-1.5 flex">
                                <input x-init="$el._x_flatpickr = flatpickr($el, {
                                    defaultDate: selectedAppointment?.appointment_date || new Date(),
                                    dateFormat: 'Y-m-d',
                                    onChange: function(selectedDates, dateStr, instance) {
                                        if (selectedAppointment) {
                                            selectedAppointment.appointment_date = dateStr;
                                        }
                                    }
                                })"
                                    x-effect="
                                        if (selectedAppointment?.appointment_date && $el._x_flatpickr) {
                                            $el._x_flatpickr.setDate(selectedAppointment.appointment_date, false);
                                        }
                                    "
                                    class="form-input peer w-full rounded-lg border border-slate-300 bg-transparent px-3 py-2 pl-9 placeholder:text-slate-400/70 hover:border-slate-400 focus:border-primary dark:border-navy-450 dark:hover:border-navy-400 dark:focus:border-accent"
                                    placeholder="Choose date..." type="text" />
                                <span
                                    class="pointer-events-none absolute flex h-full w-10 items-center justify-center text-slate-400 peer-focus:text-primary dark:text-navy-300 dark:peer-focus:text-accent">
                                    <svg xmlns="http://www.w3.org/2000/svg"
                                        class="size-5 transition-colors duration-200" fill="none" viewBox="0 0 24 24"
                                        stroke="currentColor" stroke-width="1.5">
                                        <path stroke-linecap="round" stroke-linejoin="round"
                                            d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
                                    </svg>
                                </span>
                            </label>
                        </div>


                        <div>
                            <span>Description</span>
                            <div class="mt-1.5 w-full">
                                <div class="h-36" x-init="$el._x_quill = new Quill($el, {
                                    modules: {
                                        toolbar: [
                                            ['bold', 'italic', 'underline'],
                                            [
                                                { list: 'ordered' },
                                                { list: 'bullet' },
                                                { header: 1 },
                                                { background: [] },
                                            ],
                                        ],
                                    },
                                    placeholder: 'Enter your content...',
                                    theme: 'snow',
                                })">
                                    Lorem ipsum dolor sit amet, consectetur adipisicing elit.
                                    Corporis incidunt nostrum repellat.
                                </div>
                            </div>
                        </div>
                    </div>
                    <div
                        class="flex items-center justify-between border-t border-slate-150 py-3 px-4 dark:border-navy-600">
                        <div class="flex space-x-1">
                            <button
                                class="btn size-8 rounded-full p-0 text-error hover:bg-error/20 focus:bg-error/20 active:bg-error/25">
                                <svg xmlns="http://www.w3.org/2000/svg" class="size-5" fill="none"
                                    viewBox="0 0 24 24" stroke="currentColor" stroke-width="1.5">
                                    <path stroke-linecap="round" stroke-linejoin="round"
                                        d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                                </svg>
                            </button>
                            <button
                                class="btn size-8 rounded-full p-0 hover:bg-slate-300/20 focus:bg-slate-300/20 active:bg-slate-300/25 dark:hover:bg-navy-300/20 dark:focus:bg-navy-300/20 dark:active:bg-navy-300/25">
                                <svg xmlns="http://www.w3.org/2000/svg" class="size-5" fill="none"
                                    viewBox="0 0 24 24" stroke="currentColor" stroke-width="1.5">
                                    <path stroke-linecap="round" stroke-linejoin="round"
                                        d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14" />
                                </svg>
                            </button>
                        </div>
                        <button @click="
                            if (selectedAppointment) {
                                @this.call('updateAppointment', selectedAppointment).then(() => {
                                    showDrawer = false;
                                });
                            }
                        "
                            class="btn min-w-[7rem] bg-primary font-medium text-white hover:bg-primary-focus focus:bg-primary-focus active:bg-primary-focus/90 dark:bg-accent dark:hover:bg-accent-focus dark:focus:bg-accent-focus dark:active:bg-accent/90">
                            Save
                        </button>
                    </div>
                </div>
            </div>
        </template>
    </div>
