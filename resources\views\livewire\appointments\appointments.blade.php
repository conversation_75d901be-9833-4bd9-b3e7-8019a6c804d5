<div class="contents" x-data="{
    showFilterModal: false,
    selectedAppointment: null,
}">
    <!-- Sidebar -->
    <div class="sidebar print:hidden">
        <!-- Main Sidebar -->
        <x-app-partials.main-sidebar></x-app-partials.main-sidebar>

        <!-- Sidebar Panel -->
        @include('livewire.appointments.appointments-sidebar')

        <!-- Minimized Sidebar Panel -->
        @include('livewire.appointments.appointments-minimized-sidebar')

    </div>

    <!-- App Header -->
    <x-app-partials.header></x-app-partials.header>

    <!-- Mobile Searchbar -->
    <x-app-partials.mobile-searchbar></x-app-partials.mobile-searchbar>

    <!-- Right Sidebar -->
    <x-app-partials.right-sidebar></x-app-partials.right-sidebar>

    <main class="main-content w-full px-[var(--margin-x)] pb-8">
        <div class="mt-4 grid grid-cols-1 gap-4 sm:mt-5 sm:gap-5 lg:mt-6 lg:gap-6">
            <div class="grid grid-cols-1 gap-4 sm:grid-cols-2 sm:gap-5 lg:grid-cols-4 lg:gap-6">
                <div class="card flex-row justify-between p-4">
                    <div>
                        <p class="text-xs-plus uppercase">Pending</p>
                        <div class="mt-8 flex items-baseline space-x-1">
                            <p class="text-2xl font-semibold text-slate-700 dark:text-navy-100">
                                {{ $statistics['pending_appointments']['count'] }}
                            </p>
                            @if ($statistics['pending_appointments']['today'])
                                <p class="text-xs text-success">+{{ $statistics['pending_appointments']['today'] }}</p>
                            @endif
                        </div>
                    </div>
                    <div class="mask is-squircle flex size-10 items-center justify-center bg-warning/10">
                        <i class="fa-solid fa-history text-xl text-warning"></i>
                    </div>
                    <div class="absolute bottom-0 right-0 overflow-hidden rounded-lg">
                        <i class="fa-solid fa-history translate-x-1/4 translate-y-1/4 text-5xl opacity-15"></i>
                    </div>
                </div>
                <div class="card flex-row justify-between p-4">
                    <div>
                        <p class="text-xs-plus uppercase">Confirmed</p>
                        <div class="mt-8 flex items-baseline space-x-1">
                            <p class="text-2xl font-semibold text-slate-700 dark:text-navy-100">
                                {{ $statistics['confirmed_appointments']['count'] }}
                            </p>
                            @if ($statistics['confirmed_appointments']['today'])
                                <p class="text-xs text-success">+{{ $statistics['confirmed_appointments']['today'] }}
                                </p>
                            @endif
                        </div>
                    </div>
                    <div class="mask is-squircle flex size-10 items-center justify-center bg-success/10">
                        <i class="fa-regular fa-circle-check text-xl text-success"></i>
                    </div>
                    <div class="absolute bottom-0 right-0 overflow-hidden rounded-lg">
                        <i class="fa-solid fa-circle-check translate-x-1/4 translate-y-1/4 text-5xl opacity-15"></i>
                    </div>
                </div>
                <div class="card flex-row justify-between p-4">
                    <div>
                        <p class="text-xs-plus uppercase">Cancelled</p>
                        <div class="mt-8 flex items-baseline space-x-1">
                            <p class="text-2xl font-semibold text-slate-700 dark:text-navy-100">
                                {{ $statistics['cancelled_appointments']['count'] }}
                            </p>
                            @if ($statistics['cancelled_appointments']['today'])
                                <p class="text-xs text-error">+{{ $statistics['cancelled_appointments']['today'] }}</p>
                            @endif
                        </div>
                    </div>
                    <div class="mask is-squircle flex size-10 items-center justify-center bg-error/10">
                        <svg xmlns="http://www.w3.org/2000/svg" class="size-6 text-error" fill="none"
                            viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                d="M15 12H9m12 0a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                        </svg>
                    </div>
                    <div class="absolute bottom-0 right-0 overflow-hidden rounded-lg">
                        <i class="fa-solid fa-xmark translate-x-1/4 translate-y-1/4 text-5xl opacity-15"></i>

                    </div>
                </div>
                <div class="card flex-row justify-between p-4">
                    <div>
                        <p class="text-xs-plus uppercase">Attended</p>
                        <div class="mt-8 flex items-baseline space-x-1">
                            <p class="text-2xl font-semibold text-slate-700 dark:text-navy-100">
                                {{ $statistics['completed_appointments']['count'] }}
                            </p>
                            @if ($statistics['completed_appointments']['today'])
                                <p class="text-xs text-info">+{{ $statistics['completed_appointments']['today'] }}
                                </p>
                            @endif
                        </div>
                    </div>
                    <div class="mask is-squircle flex size-10 items-center justify-center bg-info/10">
                        <i class="fa-solid fa-check-double text-xl text-info"></i>
                    </div>
                    <div class="absolute bottom-0 right-0 overflow-hidden rounded-lg">
                        <i class="fa-solid fa-check-double translate-x-1/4 translate-y-1/4 text-5xl opacity-15"></i>
                    </div>
                </div>
            </div>
            <div>
                <div class="flex items-center justify-between">
                    <h2 class="text-base font-medium tracking-wide text-slate-700 line-clamp-1 dark:text-navy-100">
                        Appointments
                    </h2>
                    <div class="flex">
                        <div class="flex items-center" x-data="{ isInputActive: false }">
                            <label class="block">
                                <input x-effect="isInputActive === true && $nextTick(() => { $el.focus()});"
                                    :class="isInputActive ? 'w-32 lg:w-48' : 'w-0'"
                                    class="form-input bg-transparent px-1 text-right transition-all duration-100 placeholder:text-slate-500 dark:placeholder:text-navy-200"
                                    placeholder="Search here..." type="text" />
                            </label>
                            <button @click="isInputActive = !isInputActive"
                                class="btn size-8 rounded-full p-0 hover:bg-slate-300/20 focus:bg-slate-300/20 active:bg-slate-300/25 dark:hover:bg-navy-300/20 dark:focus:bg-navy-300/20 dark:active:bg-navy-300/25">
                                <svg xmlns="http://www.w3.org/2000/svg" class="size-4.5" fill="none"
                                    viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5"
                                        d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                                </svg>
                            </button>
                        </div>

                        <button @click="showFilterModal = !showFilterModal"
                            class="btn size-8 rounded-full p-0 hover:bg-slate-300/20 focus:bg-slate-300/20 active:bg-slate-300/25 dark:hover:bg-navy-300/20 dark:focus:bg-navy-300/20 dark:active:bg-navy-300/25">
                            <svg xmlns="http://www.w3.org/2000/svg" class="size-4.5" fill="none" viewBox="0 0 24 24">
                                <path stroke="currentColor" stroke-linecap="round" stroke-width="2"
                                    d="M18 11.5H6M21 4H3m6 15h6" />
                            </svg>
                        </button>
                    </div>
                </div>
                <div class="card mt-3">
                    <div class="is-scrollbar-hidden min-w-full overflow-x-auto">
                        <table class="w-full text-left">
                            <thead>
                                <tr>
                                    <th
                                        class="whitespace-nowrap rounded-tl-lg bg-slate-200 px-4 py-3 font-semibold uppercase text-slate-800 dark:bg-navy-800 dark:text-navy-100 lg:px-5 w-3">
                                        #
                                    </th>
                                    <th
                                        class="whitespace-nowrap bg-slate-200 px-4 py-3 font-semibold uppercase text-slate-800 dark:bg-navy-800 dark:text-navy-100 lg:px-5">
                                        Customer
                                    </th>
                                    <th
                                        class="whitespace-nowrap bg-slate-200 px-4 py-3 font-semibold uppercase text-slate-800 dark:bg-navy-800 dark:text-navy-100 lg:px-5">
                                        Appointment in
                                    </th>
                                    <th
                                        class="whitespace-nowrap bg-slate-200 px-4 py-3 font-semibold uppercase text-slate-800 dark:bg-navy-800 dark:text-navy-100 lg:px-5">
                                        Appointment Date
                                    </th>
                                    <th
                                        class="whitespace-nowrap bg-slate-200 px-4 py-3 font-semibold uppercase text-slate-800 dark:bg-navy-800 dark:text-navy-100 lg:px-5">
                                        Service
                                    </th>
                                    <th
                                        class="whitespace-nowrap bg-slate-200 px-4 py-3 font-semibold uppercase text-slate-800 dark:bg-navy-800 dark:text-navy-100 lg:px-5">
                                        Status
                                    </th>
                                    <th
                                        class="whitespace-nowrap rounded-tr-lg bg-slate-200 px-4 py-3 font-semibold uppercase text-slate-800 dark:bg-navy-800 dark:text-navy-100 lg:px-5">
                                        Fast Actions
                                    </th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach ($appointments as $appointment)
                                    <tr class="border-y border-transparent"
                                        @click="$dispatch('show-drawer',
                                         { drawerId: 'appointments-drawer' });
                                         selectedAppointment = @js($appointment)">
                                        <td
                                            class="whitespace-nowrap
                                        px-4 py-3 sm:px-5">
                                            1</td>
                                        <td class="whitespace-nowrap px-4 py-3 sm:px-5">
                                            <div class="flex items-center space-x-3">
                                                <div class="avatar flex">
                                                    <img class="rounded-full" src="{{ asset('images/200x200.png') }}"
                                                        alt="avatar" />
                                                </div>
                                                <div class="flex flex-col">
                                                    <p class="font-medium text-slate-700 dark:text-navy-100">
                                                        {{ $appointment->customer->name }}
                                                    </p>
                                                    <p class="text-xs text-slate-400 dark:text-navy-300">
                                                        {{ $appointment->customer->phone_number }}
                                                    </p>
                                                </div>
                                            </div>
                                        </td>
                                        <td
                                            class="whitespace-nowrap px-4 py-3 font-medium text-slate-700 dark:text-navy-100 sm:px-5">
                                            <p class="font-medium text-slate-700 dark:text-navy-100">
                                                {{ $appointment->clinic->name }}
                                            </p>
                                            <p class="text-xs text-slate-400 dark:text-navy-300">Dr.
                                                {{ $appointment->doctor->name }}</p>
                                        </td>
                                        <td class="whitespace-nowrap px-4 py-3 sm:px-5">
                                            <p class="font-medium text-slate-700 dark:text-navy-100">
                                                {{ $appointment->date }}</p>
                                            <p class="text-xs text-slate-400 dark:text-navy-300">
                                                {{ $appointment->shift }}
                                            </p>
                                        </td>
                                        <td class="whitespace-nowrap px-4 py-3 sm:px-5">
                                            <p class="font-medium text-slate-700 dark:text-navy-100">
                                                {{ $appointment->service->name }}</p>
                                        </td>
                                        <td class="whitespace-nowrap px-4 py-3 sm:px-5">
                                            <div class="flex space-x-2">
                                                <div
                                                    class="badge rounded-full border {{ $appointment->status === 'confirmed' || $appointment->status === 'attended' ? 'border-success text-success' : ($appointment->status === 'pending' ? 'border-warning text-warning' : 'border-error text-error') }}">
                                                    {{ $appointment->status }}
                                                </div>
                                            </div>
                                        </td>
                                        <td class="whitespace-nowrap px-4 py-3 sm:px-5">
                                            <div class="flex space-x-2">
                                                <div class="flex items-center space-x-2 sm:space-x-3">
                                                    <label class="flex">
                                                        <input type="checkbox" @click.stop=""
                                                            class="form-checkbox is-outline size-5 rounded-full border-slate-400/70 before:bg-primary checked:border-primary hover:border-primary focus:border-primary dark:border-navy-400 dark:before:bg-accent dark:checked:border-accent dark:hover:border-accent dark:focus:border-accent">
                                                    </label>
                                                    <h2
                                                        class="cursor-pointer text-slate-600 line-clamp-1 dark:text-navy-100">
                                                        Attended
                                                    </h2>
                                                </div>
                                            </div>
                                        </td>
                                    </tr>
                                @endforeach
                            </tbody>

                        </table>
                    </div>

                    <div
                        class="flex flex-col justify-between space-y-4 px-4 py-4 sm:flex-row sm:items-center sm:space-y-0 sm:px-5">
                        <div class="text-xs-plus">1 - 10 of 10 entries</div>

                        <ol class="pagination">
                            <li class="rounded-l-full bg-slate-150 dark:bg-navy-500">
                                <a href="#"
                                    class="flex size-8 items-center justify-center rounded-full text-slate-500 transition-colors hover:bg-slate-300 focus:bg-slate-300 active:bg-slate-300/80 dark:text-navy-200 dark:hover:bg-navy-450 dark:focus:bg-navy-450 dark:active:bg-navy-450/90">
                                    <svg xmlns="http://www.w3.org/2000/svg" class="size-4" fill="none"
                                        viewBox="0 0 24 24" stroke="currentColor" stroke-width="2">
                                        <path stroke-linecap="round" stroke-linejoin="round" d="M15 19l-7-7 7-7" />
                                    </svg>
                                </a>
                            </li>
                            <li class="bg-slate-150 dark:bg-navy-500">
                                <a href="#"
                                    class="flex h-8 min-w-[2rem] items-center justify-center rounded-full px-3 leading-tight transition-colors hover:bg-slate-300 focus:bg-slate-300 active:bg-slate-300/80 dark:hover:bg-navy-450 dark:focus:bg-navy-450 dark:active:bg-navy-450/90">1</a>
                            </li>
                            <li class="bg-slate-150 dark:bg-navy-500">
                                <a href="#"
                                    class="flex h-8 min-w-[2rem] items-center justify-center rounded-full bg-primary px-3 leading-tight text-white transition-colors hover:bg-primary-focus focus:bg-primary-focus active:bg-primary-focus/90 dark:bg-accent dark:hover:bg-accent-focus dark:focus:bg-accent-focus dark:active:bg-accent/90">2</a>
                            </li>
                            <li class="bg-slate-150 dark:bg-navy-500">
                                <a href="#"
                                    class="flex h-8 min-w-[2rem] items-center justify-center rounded-full px-3 leading-tight transition-colors hover:bg-slate-300 focus:bg-slate-300 active:bg-slate-300/80 dark:hover:bg-navy-450 dark:focus:bg-navy-450 dark:active:bg-navy-450/90">3</a>
                            </li>
                            <li class="bg-slate-150 dark:bg-navy-500">
                                <a href="#"
                                    class="flex h-8 min-w-[2rem] items-center justify-center rounded-full px-3 leading-tight transition-colors hover:bg-slate-300 focus:bg-slate-300 active:bg-slate-300/80 dark:hover:bg-navy-450 dark:focus:bg-navy-450 dark:active:bg-navy-450/90">4</a>
                            </li>
                            <li class="bg-slate-150 dark:bg-navy-500">
                                <a href="#"
                                    class="flex h-8 min-w-[2rem] items-center justify-center rounded-full px-3 leading-tight transition-colors hover:bg-slate-300 focus:bg-slate-300 active:bg-slate-300/80 dark:hover:bg-navy-450 dark:focus:bg-navy-450 dark:active:bg-navy-450/90">5</a>
                            </li>
                            <li class="rounded-r-full bg-slate-150 dark:bg-navy-500">
                                <a href="#"
                                    class="flex size-8 items-center justify-center rounded-full text-slate-500 transition-colors hover:bg-slate-300 focus:bg-slate-300 active:bg-slate-300/80 dark:text-navy-200 dark:hover:bg-navy-450 dark:focus:bg-navy-450 dark:active:bg-navy-450/90">
                                    <svg xmlns="http://www.w3.org/2000/svg" class="size-4" fill="none"
                                        viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                            d="M9 5l7 7-7 7" />
                                    </svg>
                                </a>
                            </li>
                        </ol>
                    </div>
                </div>
            </div>
        </div>
    </main>
    @include('livewire.appointments.appointments-drawer')
    @include('livewire.appointments.appointments-filter-modal')

</div>
