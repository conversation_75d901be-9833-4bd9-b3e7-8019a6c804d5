   <template x-teleport="#x-teleport-target">
       <div class="fixed inset-0 z-[100] flex flex-col items-center justify-center overflow-hidden px-4 py-6 sm:px-5"
           x-show="showFilterModal" role="dialog" @keydown.window.escape="showFilterModal = false">
           <div class="absolute inset-0 bg-slate-900/60 backdrop-blur transition-opacity duration-300"
               @click="showFilterModal = false" x-show="showFilterModal" x-transition:enter="ease-out"
               x-transition:enter-start="opacity-0" x-transition:enter-end="opacity-100" x-transition:leave="ease-in"
               x-transition:leave-start="opacity-100" x-transition:leave-end="opacity-0"></div>
           <div class="relative w-full max-w-3xl rounded-lg bg-white px-4 py-10 text-center transition-opacity duration-300 dark:bg-navy-700 sm:px-5"
               x-show="showFilterModal" x-transition:enter="easy-out" x-transition:enter-start="opacity-0 scale-95"
               x-transition:enter-end="opacity-100 scale-100" x-transition:leave="easy-in"
               x-transition:leave-start="opacity-100 scale-100" x-transition:leave-end="opacity-0 scale-95">
               <div class="max-w-3xl py-3">
                   <div class="grid grid-cols-1 gap-4 sm:grid-cols-2 sm:gap-5 lg:gap-6 text-left">
                       <label class="block">
                           <span>From:</span>
                           <div class="relative mt-1.5 flex">
                               <input x-init="$el._x_flatpickr = flatpickr($el)"
                                   class="form-input peer w-full rounded-lg border border-slate-300 bg-transparent px-3 py-2 pl-9 placeholder:text-slate-400/70 hover:border-slate-400 focus:border-primary dark:border-navy-450 dark:hover:border-navy-400 dark:focus:border-accent"
                                   placeholder="Choose start date..." type="text" />
                               <span
                                   class="pointer-events-none absolute flex h-full w-10 items-center justify-center text-slate-400 peer-focus:text-primary dark:text-navy-300 dark:peer-focus:text-accent">
                                   <svg xmlns="http://www.w3.org/2000/svg" class="size-5 transition-colors duration-200"
                                       fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="1.5">
                                       <path stroke-linecap="round" stroke-linejoin="round"
                                           d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
                                   </svg>
                               </span>
                           </div>
                       </label>
                       <label class="block">
                           <span>To:</span>
                           <div class="relative mt-1.5 flex">
                               <input x-init="$el._x_flatpickr = flatpickr($el)"
                                   class="form-input peer w-full rounded-lg border border-slate-300 bg-transparent px-3 py-2 pl-9 placeholder:text-slate-400/70 hover:border-slate-400 focus:border-primary dark:border-navy-450 dark:hover:border-navy-400 dark:focus:border-accent"
                                   placeholder="Choose start date..." type="text" />
                               <div
                                   class="pointer-events-none absolute flex h-full w-10 items-center justify-center text-slate-400 peer-focus:text-primary dark:text-navy-300 dark:peer-focus:text-accent">
                                   <svg xmlns="http://www.w3.org/2000/svg" class="size-5 transition-colors duration-200"
                                       fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="1.5">
                                       <path stroke-linecap="round" stroke-linejoin="round"
                                           d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
                                   </svg>
                               </div>
                           </div>
                       </label>
                       <div class="sm:col-span-2 mt-2">
                           <span>Appointment Status:</span>
                           <div class="mt-3 grid grid-cols-1 gap-4 sm:grid-cols-4 sm:gap-5 lg:gap-6">
                               <label class="inline-flex items-center space-x-2">
                                   <input
                                       class="form-checkbox is-basic size-5 rounded-sm border-slate-400/70 checked:border-secondary checked:bg-secondary hover:border-secondary focus:border-secondary dark:border-navy-400 dark:checked:border-secondary-light dark:checked:bg-secondary-light dark:hover:border-secondary-light dark:focus:border-secondary-light"
                                       type="checkbox" />
                                   <span>Pending</span>
                               </label>
                               <label class="inline-flex items-center space-x-2">
                                   <input
                                       class="form-checkbox is-basic size-5 rounded-sm border-slate-400/70 checked:border-primary checked:bg-primary hover:border-primary focus:border-primary dark:border-navy-400 dark:checked:border-accent dark:checked:bg-accent dark:hover:border-accent dark:focus:border-accent"
                                       type="checkbox" />
                                   <span>Confirmed</span>
                               </label>
                               <label class="inline-flex items-center space-x-2">
                                   <input
                                       class="form-checkbox is-basic size-5 rounded-sm border-slate-400/70 checked:border-success! checked:bg-success hover:border-success! focus:border-success! dark:border-navy-400"
                                       type="checkbox" />
                                   <span>Attended</span>
                               </label>
                               <label class="inline-flex items-center space-x-2">
                                   <input
                                       class="form-checkbox is-basic size-5 rounded-sm border-slate-400/70 checked:border-error! checked:bg-error hover:border-error! focus:border-error! dark:border-navy-400"
                                       type="checkbox" />
                                   <span>Cancelled</span>
                               </label>
                           </div>
                       </div>
                   </div>
                   <div class="mt-4 space-x-1 text-right">
                       <button @click="showFilterModal = !showFilterModal"
                           class="btn font-medium text-slate-700 hover:bg-slate-300/20 active:bg-slate-300/25 dark:text-navy-100 dark:hover:bg-navy-300/20 dark:active:bg-navy-300/25">
                           Cancel
                       </button>

                       <button @click="showFilterModal = !showFilterModal"
                           class="btn bg-primary font-medium text-white hover:bg-primary-focus focus:bg-primary-focus active:bg-primary-focus/90 dark:bg-accent dark:hover:bg-accent-focus dark:focus:bg-accent-focus dark:active:bg-accent/90">
                           Apply
                       </button>
                   </div>
               </div>
           </div>
       </div>
   </template>
<?php /**PATH J:\Laravel_Projects\alhars_last-Rev\resources\views/livewire/appointments/appointments-filter-modal.blade.php ENDPATH**/ ?>