<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Flatpickr Test</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/flatpickr/dist/flatpickr.min.css">
    <script src="https://cdn.jsdelivr.net/npm/flatpickr"></script>
    <script src="https://unpkg.com/alpinejs@3.x.x/dist/cdn.min.js" defer></script>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            max-width: 600px;
            margin: 0 auto;
        }
        .form-input {
            width: 100%;
            padding: 10px;
            border: 1px solid #ccc;
            border-radius: 5px;
            margin: 10px 0;
        }
        .test-section {
            margin: 20px 0;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
    </style>
</head>
<body>
    <h1>Flatpickr Test</h1>
    
    <div class="test-section">
        <h2>Test 1: Basic Flatpickr</h2>
        <input id="basic-test" class="form-input" placeholder="Choose date..." type="text" />
        <script>
            flatpickr("#basic-test", {
                dateFormat: "Y-m-d",
                defaultDate: new Date()
            });
        </script>
    </div>

    <div class="test-section">
        <h2>Test 2: With Alpine.js (Similar to your code)</h2>
        <div x-data="{
            selectedAppointment: {
                appointment_date: '2024-01-15',
                id: 1
            }
        }">
            <p>Selected Date: <span x-text="selectedAppointment.appointment_date"></span></p>
            <input x-init="$el._x_flatpickr = flatpickr($el, {
                defaultDate: selectedAppointment?.appointment_date || new Date(),
                dateFormat: 'Y-m-d',
                onChange: function(selectedDates, dateStr, instance) {
                    if (selectedAppointment) {
                        selectedAppointment.appointment_date = dateStr;
                    }
                }
            })"
            x-effect="
                if (selectedAppointment?.appointment_date && $el._x_flatpickr) {
                    $el._x_flatpickr.setDate(selectedAppointment.appointment_date, false);
                }
            "
            class="form-input" placeholder="Choose date..." type="text" />
            
            <button @click="selectedAppointment.appointment_date = '2024-12-25'">
                Set to Christmas
            </button>
            <button @click="selectedAppointment.appointment_date = '2024-06-15'">
                Set to June 15
            </button>
        </div>
    </div>

    <div class="test-section">
        <h2>Test 3: Check if Flatpickr is loaded</h2>
        <script>
            if (typeof flatpickr !== 'undefined') {
                document.write('<p style="color: green;">✓ Flatpickr is loaded successfully!</p>');
            } else {
                document.write('<p style="color: red;">✗ Flatpickr is NOT loaded!</p>');
            }
        </script>
    </div>
</body>
</html>
