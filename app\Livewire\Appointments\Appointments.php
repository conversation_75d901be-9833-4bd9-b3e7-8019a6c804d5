<?php

namespace App\Livewire\Appointments;

use App\Models\Appointment;
use App\Models\Clinic;
use Livewire\Component;

class Appointments extends Component
{
    public $clinic_id;
    public $doctor_id;
    public $service_id;
    public $clinics = [];

    public $statistics = [
        'total_appointments' => [
            'count' => 0,
            'today' => 0,
        ],
        'pending_appointments' => [
            'count' => 0,
            'today' => 0,
        ],
        'confirmed_appointments' => [
            'count' => 0,
            'today' => 0,
        ],
        'completed_appointments' => [
            'count' => 0,
            'today' => 0,
        ],
        'cancelled_appointments' => [
            'count' => 0,
            'today' => 0,
        ],
    ];

    public function getClinics()
    {
        $this->clinics = Clinic::with([
            'doctors:clinic_id,id,name',
            'services:clinic_id,id,name'
        ])
            ->where('is_open', true)
            ->where('business_id', auth()->user()->business->id)
            ->select('id', 'name')
            ->get()
            ->toArray();
            // dd($this->clinics);
    }

    public function updateAppointment($appointment)
    {
        $updated = Appointment::where('id', $appointment['id'])->update($appointment);
        if ($updated) {
            $this->getStatistics();
            $this->dispatch('show-success', 'Appointment updated successfully');
            return true;
        } else {
            $this->dispatch('show-error', 'Failed to update appointment');
            return false;
        }
    }

    public function updateAppointmentStatus($appointment_id, $status)
    {
        Appointment::where('id', $appointment_id)->update(['status' => $status]);
        $this->getStatistics();
    }

    public function mount($clinic_id = null, $doctor_id = null, $service_id = null)
    {
        $this->clinic_id = $clinic_id;
        $this->doctor_id = $doctor_id;
        $this->service_id = $service_id;
        $this->getStatistics();
    }

    protected function baseAppointmentQuery()
    {
        $query = Appointment::query();

        if ($this->clinic_id) {
            $query->where('clinic_id', $this->clinic_id);
        } elseif ($this->doctor_id) {
            $query->where('doctor_id', $this->doctor_id);
        } elseif ($this->service_id) {
            $query->where('service_id', $this->service_id);
        }

        return $query;
    }


    public function getStatistics()
    {
        $query = $this->baseAppointmentQuery();

        $statuses = ['pending', 'confirmed', 'completed', 'cancelled'];

        $this->statistics['total_appointments'] = [
            'count' => $query->count(),
            'today' => (clone $query)->whereDate('created_at', today())->count(),
        ];

        foreach ($statuses as $status) {
            $this->statistics["{$status}_appointments"] = [
                'count' => (clone $query)->where('status', $status)->count(),
                'today' => (clone $query)->where('status', $status)->whereDate('created_at', today())->count(),
            ];
        }
    }


    public function render()
    {
        $appointments = $this->baseAppointmentQuery()
            ->with(['customer', 'doctor', 'service', 'clinic'])
            ->orderBy('appointment_date', 'desc')
            ->paginate(10);

        return view('livewire.appointments.appointments', compact('appointments'));
    }
}
