<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class Appointment extends Model
{
    protected $fillable = [
        'business_id',
        'customer_id',
        'doctor_id',
        'clinic_id',
        'service_id',
        'appointment_date',
        'appointment_shift',
        'reason',
        'notes',
        'status',
    ];

    protected $casts = [
        'appointment_date' => 'datetime',
    ];

    protected $appends = [
        'date',
        'shift',
    ];

    public function getShiftAttribute()
    {
        return $this->appointment_shift === 'AM' ? 'Morning' : 'Evening';
    }

    public function getDateAttribute()
    {
        $date = $this->appointment_date->startOfDay();
        $today = now()->startOfDay();
        $tomorrow = now()->addDay()->startOfDay();

        if ($date->equalTo($today)) {
            return 'Today';
        } elseif ($date->equalTo($tomorrow)) {
            return 'Tomorrow';
        }

        return $this->appointment_date->format('Y-m-d');
    }


    /**
     * Get the business that owns the appointment.
     */
    public function business(): BelongsTo
    {
        return $this->belongsTo(Business::class);
    }

    /**
     * Get the customer that owns the appointment.
     */
    public function customer(): BelongsTo
    {
        return $this->belongsTo(Customer::class);
    }

    /**
     * Get the doctor that owns the appointment.
     */
    public function doctor(): BelongsTo
    {
        return $this->belongsTo(Doctor::class);
    }

    /**
     * Get the service that owns the appointment.
     */
    public function service(): BelongsTo
    {
        return $this->belongsTo(ClinicService::class);
    }

    /**
     * Get the clinic that owns the appointment.
     */
    public function clinic(): BelongsTo
    {
        return $this->belongsTo(Clinic::class);
    }

    public function scopeSearch($query, $search)
    {
        return $query->where(function ($q) use ($search) {
            $q->where('id', 'like', "%{$search}%") // البحث برقم الحجز
                ->orWhereHas('customer', function ($query) use ($search) {
                    $query->where('first_name', 'like', "%{$search}%")
                        ->orWhere('last_name', 'like', "%{$search}%")
                        ->orWhere('phone_number', 'like', "%{$search}%")
                        ->orWhere('email', 'like', "%{$search}%");
                });
        });
    }
}
